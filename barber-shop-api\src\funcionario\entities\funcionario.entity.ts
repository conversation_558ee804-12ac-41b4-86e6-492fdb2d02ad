import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type FuncionarioDocument = Funcionario & Document;

@Schema()
export class Funcionario {
  @Prop({ type: Types.ObjectId, ref: 'Barbearia', required: true })
  barbeariaId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'Usuario', required: true })
  usuarioId: Types.ObjectId;

  @Prop({ required: true })
  nome: string;

  @Prop({ required: true, unique: true })
  cpf: string;

  @Prop({ required: true })
  email: string;

  @Prop({ default: true })
  ativo: boolean;

  @Prop({ default: Date.now })
  criadoEm: Date;

  @Prop({ default: Date.now })
  atualizadoEm: Date;
}

export const FuncionarioSchema = SchemaFactory.createForClass(Funcionario);

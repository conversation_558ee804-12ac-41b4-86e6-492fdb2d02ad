import { Injectable, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Agenda, AgendaDocument, StatusHorario } from './entities/agenda.entity';
import { CreateAgendaDto } from './dto/create-agenda.dto';
import { UpdateAgendaDto } from './dto/update-agenda.dto';
import { GerarAgendaDto } from './dto/gerar-agenda.dto';

@Injectable()
export class AgendaService {
  constructor(
    @InjectModel(Agenda.name) private agendaModel: Model<AgendaDocument>,
  ) {}

  async create(createAgendaDto: CreateAgendaDto, usuarioId: string): Promise<Agenda> {
    // Verificar se já existe horário no mesmo dia e horário
    const existeHorario = await this.agendaModel.findOne({
      usuarioId: new Types.ObjectId(usuarioId),
      data: new Date(createAgendaDto.data),
      horario: createAgendaDto.horario,
    }).exec();

    if (existeHorario) {
      throw new BadRequestException('Já existe um horário cadastrado para este dia e horário');
    }

    const agenda = new this.agendaModel({
      ...createAgendaDto,
      usuarioId: new Types.ObjectId(usuarioId),
      servicosIds: createAgendaDto.servicosIds?.map(id => new Types.ObjectId(id)) || [],
    });
    return agenda.save();
  }

  async findByDateAndUser(data: string, usuarioId: string): Promise<Agenda[]> {
    const startDate = new Date(data);
    const endDate = new Date(data);
    endDate.setDate(endDate.getDate() + 1);

    return this.agendaModel
      .find({
        usuarioId: new Types.ObjectId(usuarioId),
        data: {
          $gte: startDate,
          $lt: endDate,
        },
      })
      .populate('servicosIds')
      .sort({ horario: 1 })
      .exec();
  }

  async update(id: string, updateAgendaDto: UpdateAgendaDto, usuarioId: string): Promise<Agenda> {
    const updateData: any = { ...updateAgendaDto };
    
    if (updateAgendaDto.servicosIds) {
      updateData.servicosIds = updateAgendaDto.servicosIds.map(id => new Types.ObjectId(id));
    }
    
    updateData.atualizadoEm = new Date();

    const agenda = await this.agendaModel
      .findOneAndUpdate(
        { _id: new Types.ObjectId(id), usuarioId: new Types.ObjectId(usuarioId) },
        updateData,
        { new: true }
      )
      .populate('servicosIds')
      .exec();

    if (!agenda) {
      throw new BadRequestException('Horário não encontrado');
    }

    return agenda;
  }

  async remove(id: string, usuarioId: string): Promise<void> {
    const result = await this.agendaModel
      .deleteOne({ _id: new Types.ObjectId(id), usuarioId: new Types.ObjectId(usuarioId) })
      .exec();

    if (result.deletedCount === 0) {
      throw new BadRequestException('Horário não encontrado');
    }
  }

  async verificarConflitosGerarAgenda(gerarAgendaDto: GerarAgendaDto, usuarioId: string): Promise<{ conflitos: any[]; totalHorarios: number }> {
    const { dataInicio, dataFim, intervaloMinutos, horarioInicio, horarioFim, incluirFinalSemana } = gerarAgendaDto;

    const inicio = new Date(dataInicio);
    const fim = new Date(dataFim);

    const horariosParaCriar: any[] = [];
    const currentDate = new Date(inicio);

    while (currentDate <= fim) {
      const dayOfWeek = currentDate.getDay();
      if (!incluirFinalSemana && (dayOfWeek === 0 || dayOfWeek === 6)) {
        currentDate.setDate(currentDate.getDate() + 1);
        continue;
      }

      const horarios = this.gerarHorariosDoDia(horarioInicio, horarioFim, intervaloMinutos);

      for (const horario of horarios) {
        horariosParaCriar.push({
          data: new Date(currentDate),
          horario,
        });
      }

      currentDate.setDate(currentDate.getDate() + 1);
    }

    // Verificar conflitos com horários AGENDADOS
    const conflitos: any[] = [];
    for (const horarioItem of horariosParaCriar) {
      const existente = await this.agendaModel.findOne({
        usuarioId: new Types.ObjectId(usuarioId),
        data: horarioItem.data,
        horario: horarioItem.horario,
        status: 'AGENDADO',
      }).exec();

      if (existente) {
        conflitos.push({
          data: horarioItem.data,
          horario: horarioItem.horario,
          clienteNome: existente.clienteNome,
        });
      }
    }

    return {
      conflitos,
      totalHorarios: horariosParaCriar.length,
    };
  }

  async gerarAgenda(gerarAgendaDto: GerarAgendaDto, usuarioId: string, forcarSubstituicao: boolean = false): Promise<{ criados: number; message: string }> {
    const { dataInicio, dataFim, intervaloMinutos, horarioInicio, horarioFim, incluirFinalSemana } = gerarAgendaDto;

    // Validar período máximo de 2 meses
    const inicio = new Date(dataInicio);
    const fim = new Date(dataFim);
    const diffTime = Math.abs(fim.getTime() - inicio.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays > 60) {
      throw new BadRequestException('O período máximo para geração é de 2 meses (60 dias)');
    }

    const horariosParaCriar: any[] = [];
    const currentDate = new Date(inicio);

    while (currentDate <= fim) {
      // Verificar se deve incluir finais de semana
      const dayOfWeek = currentDate.getDay(); // 0 = domingo, 6 = sábado
      if (!incluirFinalSemana && (dayOfWeek === 0 || dayOfWeek === 6)) {
        currentDate.setDate(currentDate.getDate() + 1);
        continue;
      }

      // Gerar horários para o dia atual
      const horarios = this.gerarHorariosDoDia(horarioInicio, horarioFim, intervaloMinutos);
      
      for (const horario of horarios) {
        horariosParaCriar.push({
          usuarioId: new Types.ObjectId(usuarioId),
          data: new Date(currentDate),
          horario,
          status: StatusHorario.LIVRE,
          servicosIds: [],
          criadoEm: new Date(),
          atualizadoEm: new Date(),
        });
      }

      currentDate.setDate(currentDate.getDate() + 1);
    }

    if (forcarSubstituicao) {
      // Remover horários existentes no período e inserir novos
      await this.agendaModel.deleteMany({
        usuarioId: new Types.ObjectId(usuarioId),
        data: { $gte: inicio, $lte: fim },
      }).exec();

      await this.agendaModel.insertMany(horariosParaCriar);

      return {
        criados: horariosParaCriar.length,
        message: `${horariosParaCriar.length} horários criados com sucesso (substituindo existentes).`
      };
    } else {
      // Verificar se já existem horários para evitar duplicatas
      const horariosExistentes = await this.agendaModel.find({
        usuarioId: new Types.ObjectId(usuarioId),
        data: { $gte: inicio, $lte: fim },
      }).exec();

      const horariosExistentesSet = new Set(
        horariosExistentes.map(h => `${h.data.toISOString().split('T')[0]}_${h.horario}`)
      );

      const horariosNovos = horariosParaCriar.filter(h => {
        const key = `${h.data.toISOString().split('T')[0]}_${h.horario}`;
        return !horariosExistentesSet.has(key);
      });

      if (horariosNovos.length > 0) {
        await this.agendaModel.insertMany(horariosNovos);
      }

      return {
        criados: horariosNovos.length,
        message: `${horariosNovos.length} horários criados com sucesso. ${horariosParaCriar.length - horariosNovos.length} horários já existiam.`
      };
    }
  }

  private gerarHorariosDoDia(horarioInicio: string, horarioFim: string, intervaloMinutos: number): string[] {
    const horarios: string[] = [];

    const [horaInicio, minutoInicio] = horarioInicio.split(':').map(Number);
    const [horaFim, minutoFim] = horarioFim.split(':').map(Number);

    const inicioEmMinutos = horaInicio * 60 + minutoInicio;
    const fimEmMinutos = horaFim * 60 + minutoFim;

    // Incluir o horário final se for exatamente divisível pelo intervalo
    for (let minutos = inicioEmMinutos; minutos <= fimEmMinutos; minutos += intervaloMinutos) {
      // Parar se ultrapassar o horário final
      if (minutos > fimEmMinutos) break;

      const horas = Math.floor(minutos / 60);
      const mins = minutos % 60;
      horarios.push(`${horas.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`);
    }

    return horarios;
  }
}

"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgendaController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const agenda_service_1 = require("./agenda.service");
const create_agenda_dto_1 = require("./dto/create-agenda.dto");
const update_agenda_dto_1 = require("./dto/update-agenda.dto");
const gerar_agenda_dto_1 = require("./dto/gerar-agenda.dto");
const auth_service_1 = require("../auth/auth-service");
let AgendaController = class AgendaController {
    agendaService;
    authService;
    constructor(agendaService, authService) {
        this.agendaService = agendaService;
        this.authService = authService;
    }
    async create(createAgendaDto, authHeader) {
        const { usuario } = await this.authService.getUserFromToken(authHeader);
        return this.agendaService.create(createAgendaDto, usuario._id.toString());
    }
    async findByDate(data, authHeader) {
        if (!data) {
            throw new common_1.UnauthorizedException('Parâmetro data é obrigatório');
        }
        const { usuario } = await this.authService.getUserFromToken(authHeader);
        return this.agendaService.findByDateAndUser(data, usuario._id.toString());
    }
    async update(id, updateAgendaDto, authHeader) {
        const { usuario } = await this.authService.getUserFromToken(authHeader);
        return this.agendaService.update(id, updateAgendaDto, usuario._id.toString());
    }
    async remove(id, authHeader) {
        const { usuario } = await this.authService.getUserFromToken(authHeader);
        await this.agendaService.remove(id, usuario._id.toString());
        return { message: 'Horário removido com sucesso' };
    }
    async verificarConflitosGerarAgenda(gerarAgendaDto, authHeader) {
        const { usuario } = await this.authService.getUserFromToken(authHeader);
        return this.agendaService.verificarConflitosGerarAgenda(gerarAgendaDto, usuario._id.toString());
    }
    async gerarAgenda(body, authHeader) {
        const { forcarSubstituicao, ...gerarAgendaDto } = body;
        const { usuario } = await this.authService.getUserFromToken(authHeader);
        return this.agendaService.gerarAgenda(gerarAgendaDto, usuario._id.toString(), forcarSubstituicao || false);
    }
};
exports.AgendaController = AgendaController;
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Headers)('authorization')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_agenda_dto_1.CreateAgendaDto, String]),
    __metadata("design:returntype", Promise)
], AgendaController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiQuery)({ name: 'data', required: true, description: 'Data no formato YYYY-MM-DD' }),
    __param(0, (0, common_1.Query)('data')),
    __param(1, (0, common_1.Headers)('authorization')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], AgendaController.prototype, "findByDate", null);
__decorate([
    (0, common_1.Put)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Headers)('authorization')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_agenda_dto_1.UpdateAgendaDto, String]),
    __metadata("design:returntype", Promise)
], AgendaController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Headers)('authorization')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], AgendaController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)('gerar/verificar'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Headers)('authorization')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [gerar_agenda_dto_1.GerarAgendaDto, String]),
    __metadata("design:returntype", Promise)
], AgendaController.prototype, "verificarConflitosGerarAgenda", null);
__decorate([
    (0, common_1.Post)('gerar'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Headers)('authorization')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], AgendaController.prototype, "gerarAgenda", null);
exports.AgendaController = AgendaController = __decorate([
    (0, swagger_1.ApiTags)('Agenda'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.Controller)('agenda'),
    __metadata("design:paramtypes", [agenda_service_1.AgendaService,
        auth_service_1.AuthService])
], AgendaController);
//# sourceMappingURL=agenda.controller.js.map
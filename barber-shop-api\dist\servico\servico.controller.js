"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServicosController = void 0;
const common_1 = require("@nestjs/common");
const create_servico_dto_1 = require("./dto/create-servico.dto");
const servico_service_1 = require("./servico.service");
const swagger_1 = require("@nestjs/swagger");
let ServicosController = class ServicosController {
    servicosService;
    constructor(servicosService) {
        this.servicosService = servicosService;
    }
    create(createServicoDto) {
        return this.servicosService.create(createServicoDto);
    }
    findAll(barbeariaId) {
        return this.servicosService.findAllByBarbearia(barbeariaId);
    }
    update(id, updateData) {
        return this.servicosService.update(id, updateData);
    }
    remove(id) {
        return this.servicosService.remove(id);
    }
};
exports.ServicosController = ServicosController;
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_servico_dto_1.CreateServicoDto]),
    __metadata("design:returntype", void 0)
], ServicosController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)('barbeariaId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ServicosController.prototype, "findAll", null);
__decorate([
    (0, common_1.Put)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], ServicosController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ServicosController.prototype, "remove", null);
exports.ServicosController = ServicosController = __decorate([
    (0, swagger_1.ApiTags)("Serviços"),
    (0, common_1.Controller)('servicos'),
    __metadata("design:paramtypes", [servico_service_1.ServicosService])
], ServicosController);
//# sourceMappingURL=servico.controller.js.map
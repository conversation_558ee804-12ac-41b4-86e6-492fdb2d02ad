import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { UsuarioService } from './usuario.service';
import { Usuario, UsuarioSchema } from './entities/usuario.entity';
import { UsuarioController } from './usuario.controller';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Usuario.name, schema: UsuarioSchema }]),
  ],
  providers: [UsuarioService],
  exports: [
    UsuarioService,
    MongooseModule,
  ],
  controllers: [UsuarioController]
})
export class UsuarioModule {}

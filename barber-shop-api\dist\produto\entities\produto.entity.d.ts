import { Document, Types } from 'mongoose';
export type ProdutoDocument = Produto & Document;
export declare class Produto {
    barbeariaId: Types.ObjectId;
    nome: string;
    descricao?: string;
    valor: number;
    codigo: string;
    ativo: boolean;
    criadoEm: Date;
    atualizadoEm: Date;
}
export declare const ProdutoSchema: import("mongoose").Schema<Produto, import("mongoose").Model<Produto, any, any, any, Document<unknown, any, Produto, any> & Produto & {
    _id: Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Produto, Document<unknown, {}, import("mongoose").FlatRecord<Produto>, {}> & import("mongoose").FlatRecord<Produto> & {
    _id: Types.ObjectId;
} & {
    __v: number;
}>;

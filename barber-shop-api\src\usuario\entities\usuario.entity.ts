import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Document, Types } from "mongoose";

export type UsuarioDocument = Usuario & Document;

@Schema()
export class Usuario {

    @Prop()
    id: String;
    @Prop()
    nome: String;
    @Prop()
    senha: string;
    @Prop()
    email: String;
    @Prop()
    barbeariaId: Types.ObjectId;
}

export const UsuarioSchema = SchemaFactory.createForClass(Usuario);
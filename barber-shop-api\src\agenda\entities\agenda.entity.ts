import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type AgendaDocument = Agenda & Document;

export enum StatusHorario {
  LIVRE = 'LIVRE',
  AGENDADO = 'AGENDADO',
  BLOQUEADO = 'BLOQUEADO',
  ATENDIDO = 'ATENDIDO'
}

@Schema()
export class Agenda {
  @Prop({ type: Types.ObjectId, ref: 'Usuario', required: true })
  usuarioId: Types.ObjectId;

  @Prop({ required: true })
  data: Date;

  @Prop({ required: true })
  horario: string;

  @Prop({ 
    type: String, 
    enum: StatusHorario, 
    default: StatusHorario.LIVRE 
  })
  status: StatusHorario;

  @Prop({ required: false })
  clienteNome?: string;

  @Prop({ required: false })
  clienteEmail?: string;

  @Prop({ type: [{ type: Types.ObjectId, ref: 'Servico' }], default: [] })
  servicosIds: Types.ObjectId[];

  @Prop({ default: Date.now })
  criadoEm: Date;

  @Prop({ default: Date.now })
  atualizadoEm: Date;
}

export const AgendaSchema = SchemaFactory.createForClass(Agenda);

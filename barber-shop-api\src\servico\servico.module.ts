import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ServicosService } from './servico.service';
import { Servico, ServicoSchema } from './servico.entity';
import { ServicosController } from './servico.controller';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Servico.name, schema: ServicoSchema }]),
  ],
  controllers: [ServicosController],
  providers: [ServicosService],
})
export class ServicosModule {}

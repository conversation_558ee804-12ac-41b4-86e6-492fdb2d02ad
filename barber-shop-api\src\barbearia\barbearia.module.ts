import { Modu<PERSON> } from '@nestjs/common';
import { BarbeariaService } from './barbearia.service';
import { BarbeariaController } from './barbearia.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { BarbeariaSchema } from './entities/barbearia.entity';
import { UsuarioSchema } from 'src/usuario/entities/usuario.entity';
import { AuthModule } from 'src/auth/auth.module';

@Module({
    imports: [
    MongooseModule.forFeature([{ name: 'Barbearia', schema: BarbeariaSchema }]),
    MongooseModule.forFeature([{ name: 'Usuario', schema: UsuarioSchema }]),
    AuthModule,
  ],
  controllers: [BarbeariaController],
  providers: [BarbeariaService],
})
export class BarbeariaModule {}

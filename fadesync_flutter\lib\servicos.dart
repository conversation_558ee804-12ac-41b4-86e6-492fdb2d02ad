import 'package:flutter/material.dart';
import 'package:fadesync/barbearia_service.dart';
import 'package:fadesync/api_service.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fadesync/auth_provider.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';

class ServicosScreen extends ConsumerStatefulWidget {
  final ApiService api;

  const ServicosScreen({Key? key, required this.api}) : super(key: key);

  @override
  ConsumerState<ServicosScreen> createState() => _ServicosScreenState();
}

class _ServicosScreenState extends ConsumerState<ServicosScreen> {
  late final BarbeariaService _service;
  List<Map<String, dynamic>> _servicos = [];
  bool _loading = true;
  String _barbeariaId = '';

  @override
  void initState() {
    super.initState();
    _service = BarbeariaService(widget.api);
    _carregarServicos();
  }

  Future<void> _carregarServicos() async {
    setState(() => _loading = true);
    try {
      final authState = ref.read(authProvider);
      final barbeariaId = authState.usuario?['barbeariaId'];
      _barbeariaId = barbeariaId ?? '';
      if (barbeariaId == null) {
        throw Exception('Barbearia não encontrada no authState');
      }

      final data = await _service.fetchServicos(barbeariaId: barbeariaId);
      setState(() {
        _servicos = List<Map<String, dynamic>>.from(data);
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Erro ao carregar serviços: $e')),
      );
    } finally {
      setState(() => _loading = false);
    }
  }

  void _abrirPopupEdicao(Map<String, dynamic> servico) {
    final bool isNew = servico['_id'] == null;
    final _formKey = GlobalKey<FormState>();

    final TextEditingController nomeController =
        TextEditingController(text: servico['nome']);
    final TextEditingController precoController = TextEditingController(
      text: isNew ? '' : 'R\$ ${servico['preco'].toStringAsFixed(2).replaceAll('.', ',')}',
    );

    final precoMask = MaskTextInputFormatter(
      mask: 'R\$ ###.###,##',
      filter: {"#": RegExp(r'[0-9]')},
      type: MaskAutoCompletionType.lazy,
    );

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              isNew ? Icons.add_circle : Icons.edit,
              color: Theme.of(context).primaryColor,
            ),
            const SizedBox(width: 8),
            Text(isNew ? 'Adicionar Serviço' : 'Editar Serviço'),
          ],
        ),
        content: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: nomeController,
                decoration: const InputDecoration(
                  labelText: 'Nome do Serviço',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.content_cut),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Nome é obrigatório';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: precoController,
                inputFormatters: [precoMask],
                decoration: const InputDecoration(
                  labelText: 'Preço',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.attach_money),
                  hintText: 'R\$ 0,00',
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Preço é obrigatório';
                  }
                  final precoString = value.replaceAll(RegExp(r'[R\$\s\.]'), '').replaceAll(',', '.');
                  final preco = double.tryParse(precoString);
                  if (preco == null || preco <= 0) {
                    return 'Preço deve ser maior que zero';
                  }
                  return null;
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancelar'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (!_formKey.currentState!.validate()) {
                return;
              }

              // Converter preço de volta para double
              final precoString = precoController.text
                  .replaceAll(RegExp(r'[R\$\s\.]'), '')
                  .replaceAll(',', '.');
              final preco = double.tryParse(precoString) ?? 0.0;

              final novoServico = {
                'nome': nomeController.text.trim(),
                'preco': preco,
                'barbeariaId': _barbeariaId,
              };

              try {
                if (isNew) {
                  await _service.criarServico(novoServico);
                } else {
                  await _service.atualizarServico(servico['_id'], novoServico);
                }
                Navigator.pop(context);
                _carregarServicos();
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(isNew ? 'Serviço adicionado com sucesso!' : 'Serviço atualizado com sucesso!'),
                    backgroundColor: Colors.green,
                  ),
                );
              } catch (e) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Erro ao salvar serviço: $e'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            child: Text(isNew ? 'Adicionar' : 'Salvar'),
          ),
        ],
      ),
    );
  }

  void _adicionarServico() {
    _abrirPopupEdicao({'nome': '', 'preco': 0});
  }

  void _confirmarExclusao(Map<String, dynamic> servico) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.warning, color: Colors.red),
            SizedBox(width: 8),
            Text('Confirmar Exclusão'),
          ],
        ),
        content: Text('Deseja realmente excluir o serviço "${servico['nome']}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancelar'),
          ),
          ElevatedButton(
            onPressed: () async {
              try {
                await _service.excluirServico(servico['_id']);
                Navigator.pop(context);
                _carregarServicos();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Serviço excluído com sucesso!'),
                    backgroundColor: Colors.green,
                  ),
                );
              } catch (e) {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Erro ao excluir serviço: $e'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Excluir'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Serviços'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _adicionarServico,
            tooltip: 'Adicionar Serviço',
          ),
        ],
      ),
      body: _loading
          ? const Center(child: CircularProgressIndicator())
          : _servicos.isEmpty
              ? const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.content_cut,
                        size: 64,
                        color: Colors.grey,
                      ),
                      SizedBox(height: 16),
                      Text(
                        'Nenhum serviço cadastrado',
                        style: TextStyle(
                          fontSize: 18,
                          color: Colors.grey,
                        ),
                      ),
                      SizedBox(height: 8),
                      Text(
                        'Toque no botão + para adicionar um serviço',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                )
              : Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: ListView.builder(
                    itemCount: _servicos.length,
                    itemBuilder: (context, index) {
                      final servico = _servicos[index];
                      return Card(
                        elevation: 2,
                        margin: const EdgeInsets.symmetric(vertical: 4),
                        child: ListTile(
                          contentPadding: const EdgeInsets.all(16),
                          leading: CircleAvatar(
                            backgroundColor: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                            child: Icon(
                              Icons.content_cut,
                              color: Theme.of(context).primaryColor,
                            ),
                          ),
                          title: Text(
                            servico['nome'],
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                          subtitle: Text(
                            'R\$ ${servico['preco'].toStringAsFixed(2).replaceAll('.', ',')}',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.green[700],
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          trailing: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              IconButton(
                                icon: const Icon(Icons.edit, color: Colors.blue),
                                onPressed: () => _abrirPopupEdicao(servico),
                                tooltip: 'Editar',
                              ),
                              IconButton(
                                icon: const Icon(Icons.delete, color: Colors.red),
                                onPressed: () => _confirmarExclusao(servico),
                                tooltip: 'Excluir',
                              ),
                            ],
                          ),
                          onTap: () => _abrirPopupEdicao(servico),
                        ),
                      );
                    },
                  ),
                ),
    );
  }
}

import { FuncionarioService } from './funcionario.service';
import { CreateFuncionarioDto } from './dto/create-funcionario.dto';
import { UpdateFuncionarioDto } from './dto/update-funcionario.dto';
import { AuthService } from '../auth/auth-service';
export declare class FuncionarioController {
    private readonly funcionarioService;
    private readonly authService;
    constructor(funcionarioService: FuncionarioService, authService: AuthService);
    create(createFuncionarioDto: CreateFuncionarioDto, authHeader: string): Promise<import("./entities/funcionario.entity").Funcionario>;
    findAll(authHeader: string): Promise<import("./entities/funcionario.entity").Funcionario[]>;
    findOne(id: string, authHeader: string): Promise<import("./entities/funcionario.entity").Funcionario>;
    update(id: string, updateFuncionarioDto: UpdateFuncionarioDto, authHeader: string): Promise<import("./entities/funcionario.entity").Funcionario>;
    remove(id: string, authHeader: string): Promise<{
        message: string;
    }>;
}

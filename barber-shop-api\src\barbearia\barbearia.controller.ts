import { Controller, Get, Post, Body, Patch, Param, Delete, Headers, Put } from '@nestjs/common';
import { BarbeariaService } from './barbearia.service';
import { UpdateBarbeariaDto } from './dto/update-barbearia.dto';
import { ApiTags } from '@nestjs/swagger';

@ApiTags('Barbearia')
@Controller('barbearias')
export class BarbeariaController {
  constructor(private readonly barbeariaService: BarbeariaService) {}

  @Get()
  async findAll(@Headers('authorization') authHeader: string) {
    return this.barbeariaService.findAll(authHeader);
  }

  @Put('/:id')
  async update(@Param('id') id: string, @Body() updateBarbeariaDto: UpdateBarbeariaDto) {
    return this.barbeariaService.update(id, updateBarbeariaDto);
  }
}

import {
  Controller,
  Get,
  Post,
  Body,
  Put,
  Param,
  Delete,
  Headers,
} from '@nestjs/common';
import { ApiTags, ApiBearerAuth } from '@nestjs/swagger';
import { FuncionarioService } from './funcionario.service';
import { CreateFuncionarioDto } from './dto/create-funcionario.dto';
import { UpdateFuncionarioDto } from './dto/update-funcionario.dto';
import { AuthService } from '../auth/auth-service';

@ApiTags('Funcionario')
@ApiBearerAuth()
@Controller('funcionarios')
export class FuncionarioController {
  constructor(
    private readonly funcionarioService: FuncionarioService,
    private readonly authService: AuthService,
  ) {}

  @Post()
  async create(
    @Body() createFuncionarioDto: CreateFuncionarioDto,
    @Headers('authorization') authHeader: string,
  ) {
    const { usuario } = await this.authService.getUserFromToken(authHeader);
    return this.funcionarioService.create(createFuncionarioDto, (usuario.barbeariaId as any).toString());
  }

  @Get()
  async findAll(@Headers('authorization') authHeader: string) {
    const { usuario } = await this.authService.getUserFromToken(authHeader);
    return this.funcionarioService.findAllByBarbearia((usuario.barbeariaId as any).toString());
  }

  @Get(':id')
  async findOne(
    @Param('id') id: string,
    @Headers('authorization') authHeader: string,
  ) {
    const { usuario } = await this.authService.getUserFromToken(authHeader);
    return this.funcionarioService.findOne(id, (usuario.barbeariaId as any).toString());
  }

  @Put(':id')
  async update(
    @Param('id') id: string,
    @Body() updateFuncionarioDto: UpdateFuncionarioDto,
    @Headers('authorization') authHeader: string,
  ) {
    const { usuario } = await this.authService.getUserFromToken(authHeader);
    return this.funcionarioService.update(id, updateFuncionarioDto, (usuario.barbeariaId as any).toString());
  }

  @Delete(':id')
  async remove(
    @Param('id') id: string,
    @Headers('authorization') authHeader: string,
  ) {
    const { usuario } = await this.authService.getUserFromToken(authHeader);
    await this.funcionarioService.remove(id, (usuario.barbeariaId as any).toString());
    return { message: 'Funcionário inativado com sucesso' };
  }
}

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'agenda_service.dart';
import 'auth_provider.dart';

class Cliente {
  final String nome;
  final String email;

  Cliente({required this.nome, required this.email});
}

class Servico {
  final int id;
  final String nome;

  Servico({required this.id, required this.nome});
}

enum StatusHorario { AGENDADO, LIVRE, BLOQUEADO, ATENDIDO }

class HorarioAgenda {
  final String horario;
  final StatusHorario status;
  final Cliente? cliente;
  final List<Servico> servicos;

  HorarioAgenda({
    required this.horario,
    required this.status,
    this.cliente,
    required this.servicos,
  });
}

class AgendaScreen extends ConsumerStatefulWidget {
  const AgendaScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<AgendaScreen> createState() => _AgendaScreenState();
}

class _AgendaScreenState extends ConsumerState<AgendaScreen> {
  DateTime selectedDate = DateTime.now();
  List<HorarioAgenda> agenda = [];
  bool isLoading = false;
  String? errorMessage;

  @override
  void initState() {
    super.initState();
    _carregarAgenda();
  }

  Future<void> _carregarAgenda() async {
    setState(() {
      isLoading = true;
      errorMessage = null;
    });

    try {
      final authState = ref.read(authProvider);
      if (authState.token == null) {
        throw Exception('Token não encontrado');
      }

      final agendaService = AgendaService(authState.token!);
      final dataFormatada = DateFormat('yyyy-MM-dd').format(selectedDate);
      final agendaData = await agendaService.buscarAgendaPorData(dataFormatada);

      setState(() {
        agenda = agendaData.map((item) => _mapearHorarioAgenda(item)).toList();
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        errorMessage = e.toString();
        isLoading = false;
      });
    }
  }

  HorarioAgenda _mapearHorarioAgenda(Map<String, dynamic> data) {
    return HorarioAgenda(
      horario: data['horario'] ?? '',
      status: _mapearStatus(data['status']),
      cliente: data['clienteNome'] != null
          ? Cliente(nome: data['clienteNome'], email: data['clienteEmail'] ?? '')
          : null,
      servicos: [], // Por enquanto vazio, pode ser implementado depois
    );
  }

  StatusHorario _mapearStatus(String? status) {
    switch (status) {
      case 'AGENDADO':
        return StatusHorario.AGENDADO;
      case 'BLOQUEADO':
        return StatusHorario.BLOQUEADO;
      case 'ATENDIDO':
        return StatusHorario.ATENDIDO;
      default:
        return StatusHorario.LIVRE;
    }
  }

  Color _getColor(StatusHorario status) {
    switch (status) {
      case StatusHorario.AGENDADO:
        return const Color.fromARGB(255, 182, 179, 9);
      case StatusHorario.LIVRE:
        return Colors.grey;
      case StatusHorario.BLOQUEADO:
        return Colors.red;
      case StatusHorario.ATENDIDO:
        return Colors.green;
    }
  }

  String _formatDate(DateTime date) {
    DateTime today = DateTime.now();
    if (date.year == today.year &&
        date.month == today.month &&
        date.day == today.day) {
      return 'Hoje';
    } else {
      return DateFormat('dd/MM/yyyy').format(date);
    }
  }

  void _selectDate() async {
    DateTime? picked = await showDatePicker(
      context: context,
      initialDate: selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime(2100),
    );
    if (picked != null && picked != selectedDate) {
      setState(() {
        selectedDate = picked;
      });
      _carregarAgenda();
    }
  }

  void _showGerarAgendaModal() {
    showDialog(
      context: context,
      builder: (context) => GerarAgendaModal(
        onGerarAgenda: _gerarAgenda,
      ),
    );
  }

  Future<void> _gerarAgenda({
    required DateTime dataInicio,
    required DateTime dataFim,
    required int intervaloMinutos,
    required TimeOfDay horarioInicio,
    required TimeOfDay horarioFim,
    required bool incluirFinalSemana,
  }) async {
    try {
      final authState = ref.read(authProvider);
      if (authState.token == null) {
        throw Exception('Token não encontrado');
      }

      final agendaService = AgendaService(authState.token!);

      final resultado = await agendaService.gerarAgenda(
        dataInicio: DateFormat('yyyy-MM-dd').format(dataInicio),
        dataFim: DateFormat('yyyy-MM-dd').format(dataFim),
        intervaloMinutos: intervaloMinutos,
        horarioInicio: '${horarioInicio.hour.toString().padLeft(2, '0')}:${horarioInicio.minute.toString().padLeft(2, '0')}',
        horarioFim: '${horarioFim.hour.toString().padLeft(2, '0')}:${horarioFim.minute.toString().padLeft(2, '0')}',
        incluirFinalSemana: incluirFinalSemana,
      );

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(resultado['message'] ?? 'Agenda gerada com sucesso!')),
      );

      // Recarregar agenda se a data selecionada estiver no período gerado
      if (selectedDate.isAfter(dataInicio.subtract(const Duration(days: 1))) &&
          selectedDate.isBefore(dataFim.add(const Duration(days: 1)))) {
        _carregarAgenda();
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Erro ao gerar agenda: $e')),
      );
    }
  }

  void _showDetailDialog(HorarioAgenda item) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Serviços'),
          content: item.servicos.isEmpty
              ? const Text('Nenhum serviço associado.')
              : Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children:
                      item.servicos.map((s) => Text('- ${s.nome}')).toList(),
                ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Voltar'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: const Text('Cancelar'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Text('Agenda'),
                const Spacer(),
                IconButton(
                  onPressed: () {
                    // Ação de adicionar
                  },
                  icon: Image.asset(
                    'assets/icons/add-register.png',
                    width: 20,
                    height: 20,
                  ),
                  tooltip: 'Adicionar',
                ),
                IconButton(
                  onPressed: _selectDate,
                  icon: Image.asset(
                    'assets/icons/calendar-lines.png',
                    width: 20,
                    height: 20,
                  ),
                  tooltip: 'Selecionar data',
                ),
                IconButton(
                  onPressed: _showGerarAgendaModal,
                  icon: Image.asset(
                    'assets/icons/settings.png',
                    width: 20,
                    height: 20,
                  ),
                  tooltip: 'Configurações',
                ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              _formatDate(selectedDate),
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
          ],
        ),
        elevation: 0,
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : errorMessage != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text('Erro: $errorMessage'),
                      ElevatedButton(
                        onPressed: _carregarAgenda,
                        child: const Text('Tentar novamente'),
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  itemCount: agenda.length,
                  itemBuilder: (context, index) {
          final item = agenda[index];
          return InkWell(
            onTap: () => _showDetailDialog(item),
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Container(
                    width: 8,
                    height: 60,
                    decoration: BoxDecoration(
                      color: _getColor(item.status),
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(8),
                        bottomLeft: Radius.circular(8),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Row(
                      children: [
                        Text(
                          item.status.name,
                          style: const TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Center(
                            child: Text(
                              (item.cliente?.nome ??
                                      (item.status == StatusHorario.LIVRE
                                          ? 'Livre'
                                          : 'Bloqueado'))
                                  .toUpperCase(),
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: Text(
                      item.horario,
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}

class GerarAgendaModal extends StatefulWidget {
  final Function({
    required DateTime dataInicio,
    required DateTime dataFim,
    required int intervaloMinutos,
    required TimeOfDay horarioInicio,
    required TimeOfDay horarioFim,
    required bool incluirFinalSemana,
  }) onGerarAgenda;

  const GerarAgendaModal({Key? key, required this.onGerarAgenda}) : super(key: key);

  @override
  State<GerarAgendaModal> createState() => _GerarAgendaModalState();
}

class _GerarAgendaModalState extends State<GerarAgendaModal> {
  DateTime? dataInicio;
  DateTime? dataFim;
  int intervaloMinutos = 30;
  TimeOfDay horarioInicio = const TimeOfDay(hour: 8, minute: 0);
  TimeOfDay horarioFim = const TimeOfDay(hour: 18, minute: 0);
  bool incluirFinalSemana = false;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Gerar Agenda'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Data início
            ListTile(
              title: const Text('Data Início'),
              subtitle: Text(dataInicio != null
                  ? DateFormat('dd/MM/yyyy').format(dataInicio!)
                  : 'Selecionar data'),
              trailing: const Icon(Icons.calendar_today),
              onTap: () async {
                final picked = await showDatePicker(
                  context: context,
                  initialDate: dataInicio ?? DateTime.now(),
                  firstDate: DateTime.now(),
                  lastDate: DateTime.now().add(const Duration(days: 365)),
                );
                if (picked != null) {
                  setState(() {
                    dataInicio = picked;
                  });
                }
              },
            ),
            // Data fim
            ListTile(
              title: const Text('Data Fim'),
              subtitle: Text(dataFim != null
                  ? DateFormat('dd/MM/yyyy').format(dataFim!)
                  : 'Selecionar data'),
              trailing: const Icon(Icons.calendar_today),
              onTap: () async {
                final picked = await showDatePicker(
                  context: context,
                  initialDate: dataFim ?? dataInicio ?? DateTime.now(),
                  firstDate: dataInicio ?? DateTime.now(),
                  lastDate: DateTime.now().add(const Duration(days: 365)),
                );
                if (picked != null) {
                  setState(() {
                    dataFim = picked;
                  });
                }
              },
            ),
            // Intervalo
            ListTile(
              title: const Text('Intervalo (minutos)'),
              subtitle: DropdownButton<int>(
                value: intervaloMinutos,
                items: [15, 30, 45, 60].map((int value) {
                  return DropdownMenuItem<int>(
                    value: value,
                    child: Text('$value minutos'),
                  );
                }).toList(),
                onChanged: (int? newValue) {
                  if (newValue != null) {
                    setState(() {
                      intervaloMinutos = newValue;
                    });
                  }
                },
              ),
            ),
            // Horário início
            ListTile(
              title: const Text('Horário Início'),
              subtitle: Text('${horarioInicio.hour.toString().padLeft(2, '0')}:${horarioInicio.minute.toString().padLeft(2, '0')}'),
              trailing: const Icon(Icons.access_time),
              onTap: () async {
                final picked = await showTimePicker(
                  context: context,
                  initialTime: horarioInicio,
                );
                if (picked != null) {
                  setState(() {
                    horarioInicio = picked;
                  });
                }
              },
            ),
            // Horário fim
            ListTile(
              title: const Text('Horário Fim'),
              subtitle: Text('${horarioFim.hour.toString().padLeft(2, '0')}:${horarioFim.minute.toString().padLeft(2, '0')}'),
              trailing: const Icon(Icons.access_time),
              onTap: () async {
                final picked = await showTimePicker(
                  context: context,
                  initialTime: horarioFim,
                );
                if (picked != null) {
                  setState(() {
                    horarioFim = picked;
                  });
                }
              },
            ),
            // Incluir final de semana
            CheckboxListTile(
              title: const Text('Incluir finais de semana'),
              value: incluirFinalSemana,
              onChanged: (bool? value) {
                setState(() {
                  incluirFinalSemana = value ?? false;
                });
              },
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancelar'),
        ),
        ElevatedButton(
          onPressed: _podeGerar() ? _gerar : null,
          child: const Text('Gerar'),
        ),
      ],
    );
  }

  bool _podeGerar() {
    return dataInicio != null && dataFim != null && dataFim!.isAfter(dataInicio!);
  }

  void _gerar() {
    if (_podeGerar()) {
      // Validar período máximo de 2 meses
      final diffDays = dataFim!.difference(dataInicio!).inDays;
      if (diffDays > 60) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('O período máximo é de 2 meses (60 dias)')),
        );
        return;
      }

      widget.onGerarAgenda(
        dataInicio: dataInicio!,
        dataFim: dataFim!,
        intervaloMinutos: intervaloMinutos,
        horarioInicio: horarioInicio,
        horarioFim: horarioFim,
        incluirFinalSemana: incluirFinalSemana,
      );
      Navigator.pop(context);
    }
  }
}

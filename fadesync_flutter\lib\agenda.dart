import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';
import 'agenda_service.dart';
import 'auth_provider.dart';

class Cliente {
  final String nome;
  final String email;

  Cliente({required this.nome, required this.email});
}

class Servico {
  final int id;
  final String nome;

  Servico({required this.id, required this.nome});
}

enum StatusHorario { AGENDADO, LIVRE, BLOQUEADO, ATENDIDO, CANCELADO }

class HorarioAgenda {
  final String? id;
  final String horario;
  final StatusHorario status;
  final Cliente? cliente;
  final List<Servico> servicos;

  HorarioAgenda({
    this.id,
    required this.horario,
    required this.status,
    this.cliente,
    required this.servicos,
  });
}

class AgendaScreen extends ConsumerStatefulWidget {
  const AgendaScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<AgendaScreen> createState() => _AgendaScreenState();
}

class _AgendaScreenState extends ConsumerState<AgendaScreen> {
  DateTime selectedDate = DateTime.now();
  List<HorarioAgenda> agenda = [];
  bool isLoading = false;
  String? errorMessage;

  @override
  void initState() {
    super.initState();
    _carregarAgenda();
  }

  Future<void> _carregarAgenda() async {
    setState(() {
      isLoading = true;
      errorMessage = null;
    });

    try {
      final authState = ref.read(authProvider);
      if (authState.token == null) {
        throw Exception('Token não encontrado');
      }

      final agendaService = AgendaService(authState.token!);
      final dataFormatada = DateFormat('yyyy-MM-dd').format(selectedDate);
      final agendaData = await agendaService.buscarAgendaPorData(dataFormatada);

      setState(() {
        agenda = agendaData.map((item) => _mapearHorarioAgenda(item)).toList();
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        errorMessage = e.toString();
        isLoading = false;
      });
    }
  }

  HorarioAgenda _mapearHorarioAgenda(Map<String, dynamic> data) {
    return HorarioAgenda(
      id: data['_id'] ?? data['id'],
      horario: data['horario'] ?? '',
      status: _mapearStatus(data['status']),
      cliente: data['clienteNome'] != null
          ? Cliente(nome: data['clienteNome'], email: data['clienteEmail'] ?? '')
          : null,
      servicos: [], // Por enquanto vazio, pode ser implementado depois
    );
  }

  StatusHorario _mapearStatus(String? status) {
    switch (status) {
      case 'AGENDADO':
        return StatusHorario.AGENDADO;
      case 'BLOQUEADO':
        return StatusHorario.BLOQUEADO;
      case 'ATENDIDO':
        return StatusHorario.ATENDIDO;
      case 'CANCELADO':
        return StatusHorario.CANCELADO;
      default:
        return StatusHorario.LIVRE;
    }
  }

  Color _getColor(StatusHorario status) {
    switch (status) {
      case StatusHorario.AGENDADO:
        return const Color.fromARGB(255, 182, 179, 9);
      case StatusHorario.LIVRE:
        return Colors.grey;
      case StatusHorario.BLOQUEADO:
        return Colors.red;
      case StatusHorario.ATENDIDO:
        return Colors.green;
      case StatusHorario.CANCELADO:
        return Colors.orange;
    }
  }

  IconData _getIcon(StatusHorario status) {
    switch (status) {
      case StatusHorario.LIVRE:
        return Icons.schedule;
      case StatusHorario.AGENDADO:
        return Icons.person;
      case StatusHorario.BLOQUEADO:
        return Icons.block;
      case StatusHorario.ATENDIDO:
        return Icons.check_circle;
      case StatusHorario.CANCELADO:
        return Icons.cancel;
    }
  }

  String _getStatusText(StatusHorario status) {
    switch (status) {
      case StatusHorario.LIVRE:
        return 'Livre';
      case StatusHorario.AGENDADO:
        return 'Agendado';
      case StatusHorario.BLOQUEADO:
        return 'Bloqueado';
      case StatusHorario.ATENDIDO:
        return 'Atendido';
      case StatusHorario.CANCELADO:
        return 'Cancelado';
    }
  }

  String _formatDate(DateTime date) {
    DateTime today = DateTime.now();
    if (date.year == today.year &&
        date.month == today.month &&
        date.day == today.day) {
      return 'Hoje';
    } else {
      return DateFormat('dd/MM/yyyy').format(date);
    }
  }

  void _selectDate() async {
    DateTime? picked = await showDatePicker(
      context: context,
      initialDate: selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime(2100),
    );
    if (picked != null && picked != selectedDate) {
      setState(() {
        selectedDate = picked;
      });
      _carregarAgenda();
    }
  }

  void _showAdicionarHorarioModal() {
    showDialog(
      context: context,
      builder: (context) => AdicionarHorarioModal(
        data: selectedDate,
        onAdicionarHorario: _adicionarHorario,
      ),
    );
  }

  Future<void> _adicionarHorario({
    required String horario,
    required String status,
    String? clienteNome,
  }) async {
    try {
      final authState = ref.read(authProvider);
      if (authState.token == null) {
        throw Exception('Token não encontrado');
      }

      final agendaService = AgendaService(authState.token!);

      final dados = {
        'data': DateFormat('yyyy-MM-dd').format(selectedDate),
        'horario': horario,
        'status': status,
        if (clienteNome != null) 'clienteNome': clienteNome,
      };

      await agendaService.adicionarHorario(dados);

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Horário adicionado com sucesso!')),
      );

      _carregarAgenda();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Erro ao adicionar horário: $e')),
      );
    }
  }

  void _showGerarAgendaModal() {
    showDialog(
      context: context,
      builder: (context) => GerarAgendaModal(
        onGerarAgenda: _gerarAgenda,
      ),
    );
  }

  Future<void> _gerarAgenda({
    required DateTime dataInicio,
    required DateTime dataFim,
    required int intervaloMinutos,
    required TimeOfDay horarioInicio,
    required TimeOfDay horarioFim,
    required bool incluirFinalSemana,
  }) async {
    try {
      final authState = ref.read(authProvider);
      if (authState.token == null) {
        throw Exception('Token não encontrado');
      }

      final agendaService = AgendaService(authState.token!);

      // Primeiro verificar conflitos
      final conflitos = await agendaService.verificarConflitosGerarAgenda(
        dataInicio: DateFormat('yyyy-MM-dd').format(dataInicio),
        dataFim: DateFormat('yyyy-MM-dd').format(dataFim),
        intervaloMinutos: intervaloMinutos,
        horarioInicio: '${horarioInicio.hour.toString().padLeft(2, '0')}:${horarioInicio.minute.toString().padLeft(2, '0')}',
        horarioFim: '${horarioFim.hour.toString().padLeft(2, '0')}:${horarioFim.minute.toString().padLeft(2, '0')}',
        incluirFinalSemana: incluirFinalSemana,
      );

      if (conflitos['conflitos'].isNotEmpty) {
        // Mostrar dialog de confirmação
        final confirmar = await _mostrarDialogConflitos(conflitos);
        if (!confirmar) return;
      }

      final resultado = await agendaService.gerarAgenda(
        dataInicio: DateFormat('yyyy-MM-dd').format(dataInicio),
        dataFim: DateFormat('yyyy-MM-dd').format(dataFim),
        intervaloMinutos: intervaloMinutos,
        horarioInicio: '${horarioInicio.hour.toString().padLeft(2, '0')}:${horarioInicio.minute.toString().padLeft(2, '0')}',
        horarioFim: '${horarioFim.hour.toString().padLeft(2, '0')}:${horarioFim.minute.toString().padLeft(2, '0')}',
        incluirFinalSemana: incluirFinalSemana,
        forcarSubstituicao: conflitos['conflitos'].isNotEmpty,
      );

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(resultado['message'] ?? 'Agenda gerada com sucesso!')),
      );

      // Recarregar agenda se a data selecionada estiver no período gerado
      if (selectedDate.isAfter(dataInicio.subtract(const Duration(days: 1))) &&
          selectedDate.isBefore(dataFim.add(const Duration(days: 1)))) {
        _carregarAgenda();
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Erro ao gerar agenda: $e')),
      );
    }
  }

  Future<bool> _mostrarDialogConflitos(Map<String, dynamic> conflitos) async {
    final List<dynamic> listaConflitos = conflitos['conflitos'];

    return await showDialog<bool>(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Conflitos Encontrados'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Foram encontrados ${listaConflitos.length} horários já agendados:'),
                const SizedBox(height: 16),
                ...listaConflitos.take(5).map((conflito) {
                  final data = DateTime.parse(conflito['data']);
                  return Text(
                    '• ${DateFormat('dd/MM').format(data)} às ${conflito['horario']} - ${conflito['clienteNome'] ?? 'Cliente'}',
                    style: const TextStyle(fontSize: 12),
                  );
                }).toList(),
                if (listaConflitos.length > 5)
                  Text('... e mais ${listaConflitos.length - 5} conflitos'),
                const SizedBox(height: 16),
                const Text(
                  'Deseja continuar e substituir os horários agendados?',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('Cancelar'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.pop(context, true),
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
              child: const Text('Continuar'),
            ),
          ],
        );
      },
    ) ?? false;
  }

  void _showDetailDialog(HorarioAgenda item) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text('Horário ${item.horario}'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Status: ${item.status.name}'),
              if (item.cliente != null) ...[
                const SizedBox(height: 8),
                Text('Cliente: ${item.cliente!.nome}'),
              ],
              const SizedBox(height: 16),
              const Text('Serviços:', style: TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              item.servicos.isEmpty
                  ? const Text('Nenhum serviço associado.')
                  : Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: item.servicos.map((s) => Text('- ${s.nome}')).toList(),
                    ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Voltar'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                _showCancelarDialog(item);
              },
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: const Text('Cancelar'),
            ),
          ],
        );
      },
    );
  }

  void _showCancelarDialog(HorarioAgenda item) {
    String novoStatus = 'LIVRE';

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('Cancelar Horário'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text('Horário: ${item.horario}'),
                  const SizedBox(height: 16),
                  const Text('Selecione o novo status:'),
                  const SizedBox(height: 8),
                  DropdownButton<String>(
                    value: novoStatus,
                    isExpanded: true,
                    items: const [
                      DropdownMenuItem(value: 'LIVRE', child: Text('Livre')),
                      DropdownMenuItem(value: 'CANCELADO', child: Text('Cancelado')),
                    ],
                    onChanged: (value) {
                      setState(() {
                        novoStatus = value!;
                      });
                    },
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('Voltar'),
                ),
                ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                    _atualizarStatusHorario(item, novoStatus);
                  },
                  child: const Text('Confirmar'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Future<void> _atualizarStatusHorario(HorarioAgenda item, String novoStatus) async {
    try {
      if (item.id == null) {
        throw Exception('ID do horário não encontrado');
      }

      final authState = ref.read(authProvider);
      if (authState.token == null) {
        throw Exception('Token não encontrado');
      }

      final agendaService = AgendaService(authState.token!);

      final dados = {
        'status': novoStatus,
        if (novoStatus == 'LIVRE') ...{
          'clienteNome': null,
          'clienteEmail': null,
        }
      };

      await agendaService.atualizarHorario(item.id!, dados);

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Status atualizado com sucesso!')),
      );

      _carregarAgenda();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Erro ao atualizar status: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Text('Agenda'),
                const Spacer(),
                IconButton(
                  onPressed: _showAdicionarHorarioModal,
                  icon: Image.asset(
                    'assets/icons/add-register.png',
                    width: 20,
                    height: 20,
                  ),
                  tooltip: 'Adicionar',
                ),
                IconButton(
                  onPressed: _selectDate,
                  icon: Image.asset(
                    'assets/icons/calendar-lines.png',
                    width: 20,
                    height: 20,
                  ),
                  tooltip: 'Selecionar data',
                ),
                IconButton(
                  onPressed: _showGerarAgendaModal,
                  icon: Image.asset(
                    'assets/icons/settings.png',
                    width: 20,
                    height: 20,
                  ),
                  tooltip: 'Configurações',
                ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              _formatDate(selectedDate),
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
          ],
        ),
        elevation: 0,
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : errorMessage != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text('Erro: $errorMessage'),
                      ElevatedButton(
                        onPressed: _carregarAgenda,
                        child: const Text('Tentar novamente'),
                      ),
                    ],
                  ),
                )
              : agenda.isEmpty
                  ? const Center(
                      child: Text(
                        'Não há horários registrados nesse dia',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey,
                        ),
                      ),
                    )
                  : Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: ListView.builder(
                        itemCount: agenda.length,
                        itemBuilder: (context, index) {
                          final item = agenda[index];
                          return Card(
                            elevation: 2,
                            margin: const EdgeInsets.symmetric(vertical: 4),
                            child: InkWell(
                              onTap: () => _showDetailDialog(item),
                              borderRadius: BorderRadius.circular(8),
                              child: Container(
                                padding: const EdgeInsets.all(16),
                                child: Row(
                                  children: [
                                    // Indicador de status
                                    Container(
                                      width: 4,
                                      height: 50,
                                      decoration: BoxDecoration(
                                        color: _getColor(item.status),
                                        borderRadius: BorderRadius.circular(2),
                                      ),
                                    ),
                                    const SizedBox(width: 16),
                                    // Avatar com ícone
                                    CircleAvatar(
                                      backgroundColor: _getColor(item.status).withValues(alpha: 0.1),
                                      child: Icon(
                                        _getIcon(item.status),
                                        color: _getColor(item.status),
                                        size: 20,
                                      ),
                                    ),
                                    const SizedBox(width: 16),
                                    // Informações principais
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            item.cliente?.nome ?? _getStatusText(item.status),
                                            style: const TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.bold,
                                            ),
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                          const SizedBox(height: 4),
                                          Row(
                                            children: [
                                              Icon(
                                                Icons.access_time,
                                                size: 14,
                                                color: Colors.grey[600],
                                              ),
                                              const SizedBox(width: 4),
                                              Text(
                                                item.horario,
                                                style: TextStyle(
                                                  fontSize: 14,
                                                  color: Colors.grey[600],
                                                  fontWeight: FontWeight.w500,
                                                ),
                                              ),
                                              const SizedBox(width: 16),
                                              Container(
                                                padding: const EdgeInsets.symmetric(
                                                  horizontal: 8,
                                                  vertical: 2,
                                                ),
                                                decoration: BoxDecoration(
                                                  color: _getColor(item.status).withValues(alpha: 0.1),
                                                  borderRadius: BorderRadius.circular(12),
                                                ),
                                                child: Text(
                                                  _getStatusText(item.status),
                                                  style: TextStyle(
                                                    fontSize: 12,
                                                    color: _getColor(item.status),
                                                    fontWeight: FontWeight.w500,
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                    // Ícone de ação
                                    Icon(
                                      Icons.chevron_right,
                                      color: Colors.grey[400],
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
    );
  }
}

class GerarAgendaModal extends StatefulWidget {
  final Function({
    required DateTime dataInicio,
    required DateTime dataFim,
    required int intervaloMinutos,
    required TimeOfDay horarioInicio,
    required TimeOfDay horarioFim,
    required bool incluirFinalSemana,
  }) onGerarAgenda;

  const GerarAgendaModal({Key? key, required this.onGerarAgenda}) : super(key: key);

  @override
  State<GerarAgendaModal> createState() => _GerarAgendaModalState();
}

class _GerarAgendaModalState extends State<GerarAgendaModal> {
  DateTime? dataInicio;
  DateTime? dataFim;
  int intervaloMinutos = 30;
  TimeOfDay horarioInicio = const TimeOfDay(hour: 8, minute: 0);
  TimeOfDay horarioFim = const TimeOfDay(hour: 18, minute: 0);
  bool incluirFinalSemana = false;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Gerar Agenda'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Data início
            ListTile(
              title: const Text('Data Início'),
              subtitle: Text(dataInicio != null
                  ? DateFormat('dd/MM/yyyy').format(dataInicio!)
                  : 'Selecionar data'),
              trailing: const Icon(Icons.calendar_today),
              onTap: () async {
                final picked = await showDatePicker(
                  context: context,
                  initialDate: dataInicio ?? DateTime.now(),
                  firstDate: DateTime.now(),
                  lastDate: DateTime.now().add(const Duration(days: 365)),
                );
                if (picked != null) {
                  setState(() {
                    dataInicio = picked;
                  });
                }
              },
            ),
            // Data fim
            ListTile(
              title: const Text('Data Fim'),
              subtitle: Text(dataFim != null
                  ? DateFormat('dd/MM/yyyy').format(dataFim!)
                  : 'Selecionar data'),
              trailing: const Icon(Icons.calendar_today),
              onTap: () async {
                final picked = await showDatePicker(
                  context: context,
                  initialDate: dataFim ?? dataInicio ?? DateTime.now(),
                  firstDate: dataInicio ?? DateTime.now(),
                  lastDate: DateTime.now().add(const Duration(days: 365)),
                );
                if (picked != null) {
                  setState(() {
                    dataFim = picked;
                  });
                }
              },
            ),
            // Intervalo
            ListTile(
              title: const Text('Intervalo (minutos)'),
              subtitle: DropdownButton<int>(
                value: intervaloMinutos,
                items: [15, 30, 45, 60].map((int value) {
                  return DropdownMenuItem<int>(
                    value: value,
                    child: Text('$value minutos'),
                  );
                }).toList(),
                onChanged: (int? newValue) {
                  if (newValue != null) {
                    setState(() {
                      intervaloMinutos = newValue;
                    });
                  }
                },
              ),
            ),
            // Horário início
            ListTile(
              title: const Text('Horário Início'),
              subtitle: Text('${horarioInicio.hour.toString().padLeft(2, '0')}:${horarioInicio.minute.toString().padLeft(2, '0')}'),
              trailing: const Icon(Icons.access_time),
              onTap: () async {
                final picked = await showTimePicker(
                  context: context,
                  initialTime: horarioInicio,
                );
                if (picked != null) {
                  setState(() {
                    horarioInicio = picked;
                  });
                }
              },
            ),
            // Horário fim
            ListTile(
              title: const Text('Horário Fim'),
              subtitle: Text('${horarioFim.hour.toString().padLeft(2, '0')}:${horarioFim.minute.toString().padLeft(2, '0')}'),
              trailing: const Icon(Icons.access_time),
              onTap: () async {
                final picked = await showTimePicker(
                  context: context,
                  initialTime: horarioFim,
                );
                if (picked != null) {
                  setState(() {
                    horarioFim = picked;
                  });
                }
              },
            ),
            // Incluir final de semana
            CheckboxListTile(
              title: const Text('Incluir finais de semana'),
              value: incluirFinalSemana,
              onChanged: (bool? value) {
                setState(() {
                  incluirFinalSemana = value ?? false;
                });
              },
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancelar'),
        ),
        ElevatedButton(
          onPressed: _podeGerar() ? _gerar : null,
          child: const Text('Gerar'),
        ),
      ],
    );
  }

  bool _podeGerar() {
    if (dataInicio == null || dataFim == null) return false;
    if (!dataFim!.isAfter(dataInicio!)) return false;

    // Validar horários
    final inicioMinutos = horarioInicio.hour * 60 + horarioInicio.minute;
    final fimMinutos = horarioFim.hour * 60 + horarioFim.minute;

    return fimMinutos > inicioMinutos;
  }

  void _gerar() {
    if (!_podeGerar()) {
      String mensagem = 'Verifique os dados informados';

      if (dataInicio == null || dataFim == null) {
        mensagem = 'Selecione as datas de início e fim';
      } else if (!dataFim!.isAfter(dataInicio!)) {
        mensagem = 'A data fim deve ser posterior à data início';
      } else {
        final inicioMinutos = horarioInicio.hour * 60 + horarioInicio.minute;
        final fimMinutos = horarioFim.hour * 60 + horarioFim.minute;
        if (fimMinutos <= inicioMinutos) {
          mensagem = 'O horário fim deve ser posterior ao horário início';
        }
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(mensagem)),
      );
      return;
    }

    // Validar período máximo de 2 meses
    final diffDays = dataFim!.difference(dataInicio!).inDays;
    if (diffDays > 60) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('O período máximo é de 2 meses (60 dias)')),
      );
      return;
    }

    widget.onGerarAgenda(
      dataInicio: dataInicio!,
      dataFim: dataFim!,
      intervaloMinutos: intervaloMinutos,
      horarioInicio: horarioInicio,
      horarioFim: horarioFim,
      incluirFinalSemana: incluirFinalSemana,
    );
    Navigator.pop(context);
  }
}

class AdicionarHorarioModal extends StatefulWidget {
  final DateTime data;
  final Function({
    required String horario,
    required String status,
    String? clienteNome,
  }) onAdicionarHorario;

  const AdicionarHorarioModal({
    Key? key,
    required this.data,
    required this.onAdicionarHorario,
  }) : super(key: key);

  @override
  State<AdicionarHorarioModal> createState() => _AdicionarHorarioModalState();
}

class _AdicionarHorarioModalState extends State<AdicionarHorarioModal> {
  final _formKey = GlobalKey<FormState>();
  final _horarioController = TextEditingController();
  final _clienteNomeController = TextEditingController();
  String _status = 'LIVRE';

  final _horarioMask = MaskTextInputFormatter(
    mask: '##:##',
    filter: {"#": RegExp(r'[0-9]')},
  );

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Adicionar Horário - ${DateFormat('dd/MM/yyyy').format(widget.data)}'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: _horarioController,
                inputFormatters: [_horarioMask],
                keyboardType: TextInputType.number,
                decoration: const InputDecoration(
                  labelText: 'Horário',
                  hintText: 'HH:MM',
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Horário é obrigatório';
                  }
                  if (value.length != 5) {
                    return 'Formato inválido (use HH:MM)';
                  }
                  // Validação de horário válido
                  final parts = value.split(':');
                  final hour = int.tryParse(parts[0]);
                  final minute = int.tryParse(parts[1]);

                  if (hour == null || minute == null || hour > 23 || minute > 59) {
                    return 'Horário inválido';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _status,
                decoration: const InputDecoration(labelText: 'Status'),
                items: const [
                  DropdownMenuItem(value: 'LIVRE', child: Text('Livre')),
                  DropdownMenuItem(value: 'AGENDADO', child: Text('Agendado')),
                  DropdownMenuItem(value: 'BLOQUEADO', child: Text('Bloqueado')),
                  DropdownMenuItem(value: 'ATENDIDO', child: Text('Atendido')),
                ],
                onChanged: (value) {
                  setState(() {
                    _status = value!;
                  });
                },
              ),
              if (_status == 'AGENDADO' || _status == 'ATENDIDO') ...[
                const SizedBox(height: 16),
                TextFormField(
                  controller: _clienteNomeController,
                  decoration: const InputDecoration(labelText: 'Nome do Cliente'),
                  validator: (value) {
                    if ((_status == 'AGENDADO' || _status == 'ATENDIDO') &&
                        (value == null || value.isEmpty)) {
                      return 'Nome do cliente é obrigatório';
                    }
                    return null;
                  },
                ),

              ],
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancelar'),
        ),
        ElevatedButton(
          onPressed: _salvar,
          child: const Text('Salvar'),
        ),
      ],
    );
  }

  void _salvar() {
    if (_formKey.currentState!.validate()) {
      widget.onAdicionarHorario(
        horario: _horarioController.text,
        status: _status,
        clienteNome: _clienteNomeController.text.isNotEmpty ? _clienteNomeController.text : null,
      );
      Navigator.pop(context);
    }
  }

  @override
  void dispose() {
    _horarioController.dispose();
    _clienteNomeController.dispose();
    super.dispose();
  }
}

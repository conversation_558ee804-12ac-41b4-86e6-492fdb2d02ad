import { 
  Controller, 
  Get, 
  Post, 
  Body, 
  Put, 
  Param, 
  Delete, 
  Headers, 
  Query,
  UnauthorizedException 
} from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { AgendaService } from './agenda.service';
import { CreateAgendaDto } from './dto/create-agenda.dto';
import { UpdateAgendaDto } from './dto/update-agenda.dto';
import { GerarAgendaDto } from './dto/gerar-agenda.dto';
import { AuthService } from '../auth/auth-service';

@ApiTags('Agenda')
@ApiBearerAuth()
@Controller('agenda')
export class AgendaController {
  constructor(
    private readonly agendaService: AgendaService,
    private readonly authService: AuthService,
  ) {}

  @Post()
  async create(
    @Body() createAgendaDto: CreateAgendaDto,
    @Headers('authorization') authHeader: string,
  ) {
    const { usuario } = await this.authService.getUserFromToken(authHeader);
    return this.agendaService.create(createAgendaDto, (usuario._id as any).toString());
  }

  @Get()
  @ApiQuery({ name: 'data', required: true, description: 'Data no formato YYYY-MM-DD' })
  async findByDate(
    @Query('data') data: string,
    @Headers('authorization') authHeader: string,
  ) {
    if (!data) {
      throw new UnauthorizedException('Parâmetro data é obrigatório');
    }

    const { usuario } = await this.authService.getUserFromToken(authHeader);
    return this.agendaService.findByDateAndUser(data, (usuario._id as any).toString());
  }

  @Put(':id')
  async update(
    @Param('id') id: string,
    @Body() updateAgendaDto: UpdateAgendaDto,
    @Headers('authorization') authHeader: string,
  ) {
    const { usuario } = await this.authService.getUserFromToken(authHeader);
    return this.agendaService.update(id, updateAgendaDto, (usuario._id as any).toString());
  }

  @Delete(':id')
  async remove(
    @Param('id') id: string,
    @Headers('authorization') authHeader: string,
  ) {
    const { usuario } = await this.authService.getUserFromToken(authHeader);
    await this.agendaService.remove(id, (usuario._id as any).toString());
    return { message: 'Horário removido com sucesso' };
  }

  @Post('gerar')
  async gerarAgenda(
    @Body() gerarAgendaDto: GerarAgendaDto,
    @Headers('authorization') authHeader: string,
  ) {
    const { usuario } = await this.authService.getUserFromToken(authHeader);
    return this.agendaService.gerarAgenda(gerarAgendaDto, (usuario._id as any).toString());
  }
}

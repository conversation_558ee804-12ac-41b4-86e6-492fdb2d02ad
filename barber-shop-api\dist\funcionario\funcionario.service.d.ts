import { Model } from 'mongoose';
import { Funcionario, FuncionarioDocument } from './entities/funcionario.entity';
import { UsuarioDocument } from '../usuario/entities/usuario.entity';
import { CreateFuncionarioDto } from './dto/create-funcionario.dto';
import { UpdateFuncionarioDto } from './dto/update-funcionario.dto';
export declare class FuncionarioService {
    private funcionarioModel;
    private usuarioModel;
    constructor(funcionarioModel: Model<FuncionarioDocument>, usuarioModel: Model<UsuarioDocument>);
    create(createFuncionarioDto: CreateFuncionarioDto, barbeariaId: string): Promise<Funcionario>;
    findAllByBarbearia(barbeariaId: string): Promise<Funcionario[]>;
    findOne(id: string, barbeariaId: string): Promise<Funcionario>;
    update(id: string, updateFuncionarioDto: UpdateFuncionarioDto, barbeariaId: string): Promise<Funcionario>;
    remove(id: string, barbeariaId: string): Promise<void>;
    private gerarSenhaTemporaria;
}

import { Document, Types } from 'mongoose';
export type VendaDocument = Venda & Document;
export declare class Venda {
    barbeariaId: Types.ObjectId;
    produtoId: Types.ObjectId;
    usuarioId: Types.ObjectId;
    valorOriginal: number;
    valorVenda: number;
    quantidade: number;
    dataVenda: Date;
    criadoEm: Date;
}
export declare const VendaSchema: import("mongoose").Schema<Venda, import("mongoose").Model<Venda, any, any, any, Document<unknown, any, Venda, any> & Venda & {
    _id: Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Venda, Document<unknown, {}, import("mongoose").FlatRecord<Venda>, {}> & import("mongoose").FlatRecord<Venda> & {
    _id: Types.ObjectId;
} & {
    __v: number;
}>;

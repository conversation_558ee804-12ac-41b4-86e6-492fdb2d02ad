import { VendaService } from './venda.service';
import { CreateVendaDto } from './dto/create-venda.dto';
import { AuthService } from '../auth/auth-service';
export declare class VendaController {
    private readonly vendaService;
    private readonly authService;
    constructor(vendaService: VendaService, authService: AuthService);
    create(createVendaDto: CreateVendaDto, authHeader: string): Promise<import("./entities/venda.entity").Venda>;
    findAll(authHeader: string, dataInicio?: string, dataFim?: string): Promise<import("./entities/venda.entity").Venda[]>;
    getResumo(authHeader: string): Promise<any>;
}

import { Injectable, ConflictException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import * as bcrypt from 'bcrypt';

import { CreateUsuarioDto } from './dto/create-usuario.dto';
import { Usuario, UsuarioDocument } from './entities/usuario.entity';

@Injectable()
export class UsuarioService {
  constructor(
    @InjectModel(Usuario.name) private userModel: Model<UsuarioDocument>,
  ) {}

  async create(createUsuarioDto: CreateUsuarioDto) {
    const existsUser = await this.userModel.findOne({ email: createUsuarioDto.email }).exec();
    if (existsUser) {
      throw new ConflictException('Já existe um usuário com esse login');
    }

    const hashedPassword = await bcrypt.hash(createUsuarioDto.senha, 10);

    const newUser = new this.userModel({
      ...createUsuarioDto,
      senha: hashedPassword,
    });

    return newUser.save();
  }
}

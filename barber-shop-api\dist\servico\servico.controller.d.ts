import { CreateServicoDto } from './dto/create-servico.dto';
import { ServicosService } from './servico.service';
export declare class ServicosController {
    private readonly servicosService;
    constructor(servicosService: ServicosService);
    create(createServicoDto: CreateServicoDto): Promise<import("./servico.entity").Servico>;
    findAll(barbeariaId: string): Promise<import("./servico.entity").Servico[]>;
    update(id: string, updateData: Partial<CreateServicoDto>): Promise<import("./servico.entity").Servico | null>;
    remove(id: string): Promise<void>;
}

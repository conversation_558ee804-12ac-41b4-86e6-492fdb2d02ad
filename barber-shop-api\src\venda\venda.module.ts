import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { VendaService } from './venda.service';
import { VendaController } from './venda.controller';
import { Venda, VendaSchema } from './entities/venda.entity';
import { Produto, ProdutoSchema } from '../produto/entities/produto.entity';
import { AuthModule } from '../auth/auth.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Venda.name, schema: VendaSchema },
      { name: Produto.name, schema: ProdutoSchema },
    ]),
    AuthModule,
  ],
  controllers: [VendaController],
  providers: [VendaService],
  exports: [VendaService],
})
export class VendaModule {}

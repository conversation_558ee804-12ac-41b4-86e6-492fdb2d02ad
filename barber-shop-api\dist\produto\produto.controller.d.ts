import { ProdutoService } from './produto.service';
import { CreateProdutoDto } from './dto/create-produto.dto';
import { UpdateProdutoDto } from './dto/update-produto.dto';
import { AuthService } from '../auth/auth-service';
export declare class ProdutoController {
    private readonly produtoService;
    private readonly authService;
    constructor(produtoService: ProdutoService, authService: AuthService);
    create(createProdutoDto: CreateProdutoDto, authHeader: string): Promise<import("./entities/produto.entity").Produto>;
    findAll(authHeader: string, nome?: string, valorMin?: number, valorMax?: number, codigo?: string): Promise<import("./entities/produto.entity").Produto[]>;
    findOne(id: string, authHeader: string): Promise<import("./entities/produto.entity").Produto>;
    update(id: string, updateProdutoDto: UpdateProdutoDto, authHeader: string): Promise<import("./entities/produto.entity").Produto>;
    remove(id: string, authHeader: string): Promise<{
        message: string;
    }>;
}

import { Injectable, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Model } from 'mongoose';
import { Usuario, UsuarioDocument } from 'src/usuario/entities/usuario.entity';
import * as bcrypt from 'bcrypt';
import { InjectModel } from '@nestjs/mongoose';

@Injectable()
export class AuthService {
  constructor(
    private jwtService: JwtService,
    @InjectModel(Usuario.name) private userModel: Model<UsuarioDocument>
  ) { }

  async validateUser(username: string, pass: string): Promise<any> {
    const user = await this.userModel.findOne({ email: username }).exec();
    if (user && (await bcrypt.compare(pass, user.senha))) {
      const { senha, ...result } = user.toObject();
      return result;
    }
    return null;
  }

  async login(user: any) {
    const payload = { username: user.email, sub: user._id.toString() };
    console.log(payload);
    return {
      token: this.jwtService.sign(payload),
      usuario: {
        id: user._id.toString(),
        nome: user.nome,
        email: user.email,
        barbeariaId: user.barbeariaId,
      },
      permissoes: [],
    };
  }

  async getUserFromToken(token: string) {
    try {
      if (token.startsWith('Bearer ')) {
        token = token.slice(7);
      }
      const payload = this.jwtService.verify(token);

      if (!payload.sub) {
        throw new UnauthorizedException('Token inválido: sub ausente');
      }

      const user = await this.userModel.findOne({ email: payload.username }).exec();
      if (!user) {
        throw new UnauthorizedException('Usuário não encontrado');
      }

      const { senha, ...userData } = user.toObject();
      return {
        usuario: userData,
        permissoes: [],
      };
    } catch (err) {
      throw new UnauthorizedException('Token inválido ou expirado');
    }
  }
}

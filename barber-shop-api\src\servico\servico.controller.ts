import { Controller, Get, Post, Body, Param, Patch, Delete, Query, Put } from '@nestjs/common';
import { CreateServicoDto } from './dto/create-servico.dto';
import { ServicosService } from './servico.service';
import { ApiTags } from '@nestjs/swagger';

@ApiTags("Serviços")
@Controller('servicos')
export class ServicosController {
  constructor(private readonly servicosService: ServicosService) {}

  @Post()
  create(@Body() createServicoDto: CreateServicoDto) {
    return this.servicosService.create(createServicoDto);
  }

  @Get()
  findAll(@Query('barbeariaId') barbeariaId: string) {
    return this.servicosService.findAllByBarbearia(barbeariaId);
  }

  @Put(':id')
  update(@Param('id') id: string, @Body() updateData: Partial<CreateServicoDto>) {
    return this.servicosService.update(id, updateData);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.servicosService.remove(id);
  }
}

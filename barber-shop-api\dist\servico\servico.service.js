"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServicosService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const servico_entity_1 = require("./servico.entity");
let ServicosService = class ServicosService {
    servicoModel;
    constructor(servicoModel) {
        this.servicoModel = servicoModel;
    }
    async create(createServicoDto) {
        const createdServico = new this.servicoModel(createServicoDto);
        createdServico.barbeariaId = new mongoose_2.Types.ObjectId(createServicoDto.barbeariaId);
        return createdServico.save();
    }
    async findAllByBarbearia(barbeariaId) {
        return await this.servicoModel.find({ barbeariaId: new mongoose_2.Types.ObjectId(barbeariaId) }).exec();
    }
    async update(id, updateData) {
        const dataToUpdate = { ...updateData };
        if (dataToUpdate.barbeariaId) {
            dataToUpdate.barbeariaId = new mongoose_2.Types.ObjectId(dataToUpdate.barbeariaId);
        }
        return this.servicoModel.findByIdAndUpdate(id, dataToUpdate, { new: true }).exec();
    }
    async remove(id) {
        this.servicoModel.findByIdAndDelete({ _id: id }).exec();
    }
};
exports.ServicosService = ServicosService;
exports.ServicosService = ServicosService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(servico_entity_1.Servico.name)),
    __metadata("design:paramtypes", [mongoose_2.Model])
], ServicosService);
//# sourceMappingURL=servico.service.js.map
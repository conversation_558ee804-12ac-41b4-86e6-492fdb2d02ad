import { Injectable, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Produto, ProdutoDocument } from './entities/produto.entity';
import { CreateProdutoDto } from './dto/create-produto.dto';
import { UpdateProdutoDto } from './dto/update-produto.dto';

@Injectable()
export class ProdutoService {
  constructor(
    @InjectModel(Produto.name) private produtoModel: Model<ProdutoDocument>,
  ) {}

  async create(createProdutoDto: CreateProdutoDto, barbeariaId: string): Promise<Produto> {
    // Gerar código sequencial
    const ultimoProduto = await this.produtoModel
      .findOne({ barbeariaId: new Types.ObjectId(barbeariaId) })
      .sort({ codigo: -1 })
      .exec();

    let proximoCodigo = 1;
    if (ultimoProduto && ultimoProduto.codigo) {
      const ultimoNumero = parseInt(ultimoProduto.codigo.replace(/\D/g, ''));
      proximoCodigo = ultimoNumero + 1;
    }

    const codigo = proximoCodigo.toString().padStart(6, '0');

    const produto = new this.produtoModel({
      ...createProdutoDto,
      barbeariaId: new Types.ObjectId(barbeariaId),
      codigo,
    });

    return produto.save();
  }

  async findAllByBarbearia(
    barbeariaId: string,
    filtros?: {
      nome?: string;
      valorMin?: number;
      valorMax?: number;
      codigo?: string;
    }
  ): Promise<Produto[]> {
    const query: any = {
      barbeariaId: new Types.ObjectId(barbeariaId),
      ativo: true,
    };

    if (filtros?.nome) {
      query.nome = { $regex: filtros.nome, $options: 'i' };
    }

    if (filtros?.valorMin !== undefined || filtros?.valorMax !== undefined) {
      query.valor = {};
      if (filtros.valorMin !== undefined) {
        query.valor.$gte = filtros.valorMin;
      }
      if (filtros.valorMax !== undefined) {
        query.valor.$lte = filtros.valorMax;
      }
    }

    if (filtros?.codigo) {
      query.codigo = { $regex: filtros.codigo, $options: 'i' };
    }

    return this.produtoModel.find(query).sort({ criadoEm: -1 }).exec();
  }

  async findOne(id: string, barbeariaId: string): Promise<Produto> {
    const produto = await this.produtoModel
      .findOne({
        _id: new Types.ObjectId(id),
        barbeariaId: new Types.ObjectId(barbeariaId),
        ativo: true,
      })
      .exec();

    if (!produto) {
      throw new BadRequestException('Produto não encontrado');
    }

    return produto;
  }

  async update(id: string, updateProdutoDto: UpdateProdutoDto, barbeariaId: string): Promise<Produto> {
    const produto = await this.produtoModel
      .findOneAndUpdate(
        {
          _id: new Types.ObjectId(id),
          barbeariaId: new Types.ObjectId(barbeariaId),
        },
        { ...updateProdutoDto, atualizadoEm: new Date() },
        { new: true }
      )
      .exec();

    if (!produto) {
      throw new BadRequestException('Produto não encontrado');
    }

    return produto;
  }

  async remove(id: string, barbeariaId: string): Promise<void> {
    const result = await this.produtoModel
      .findOneAndUpdate(
        {
          _id: new Types.ObjectId(id),
          barbeariaId: new Types.ObjectId(barbeariaId),
        },
        { ativo: false, atualizadoEm: new Date() },
        { new: true }
      )
      .exec();

    if (!result) {
      throw new BadRequestException('Produto não encontrado');
    }
  }
}

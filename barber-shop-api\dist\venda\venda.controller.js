"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VendaController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const venda_service_1 = require("./venda.service");
const create_venda_dto_1 = require("./dto/create-venda.dto");
const auth_service_1 = require("../auth/auth-service");
let VendaController = class VendaController {
    vendaService;
    authService;
    constructor(vendaService, authService) {
        this.vendaService = vendaService;
        this.authService = authService;
    }
    async create(createVendaDto, authHeader) {
        const { usuario } = await this.authService.getUserFromToken(authHeader);
        return this.vendaService.create(createVendaDto, usuario._id.toString(), usuario.barbeariaId.toString());
    }
    async findAll(authHeader, dataInicio, dataFim) {
        const { usuario } = await this.authService.getUserFromToken(authHeader);
        const barbeariaId = usuario.barbeariaId.toString();
        if (dataInicio && dataFim) {
            return this.vendaService.findByDateRange(barbeariaId, new Date(dataInicio), new Date(dataFim));
        }
        return this.vendaService.findAllByBarbearia(barbeariaId);
    }
    async getResumo(authHeader) {
        const { usuario } = await this.authService.getUserFromToken(authHeader);
        return this.vendaService.getResumoVendas(usuario.barbeariaId.toString());
    }
};
exports.VendaController = VendaController;
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Headers)('authorization')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_venda_dto_1.CreateVendaDto, String]),
    __metadata("design:returntype", Promise)
], VendaController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiQuery)({ name: 'dataInicio', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'dataFim', required: false }),
    __param(0, (0, common_1.Headers)('authorization')),
    __param(1, (0, common_1.Query)('dataInicio')),
    __param(2, (0, common_1.Query)('dataFim')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], VendaController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('resumo'),
    __param(0, (0, common_1.Headers)('authorization')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], VendaController.prototype, "getResumo", null);
exports.VendaController = VendaController = __decorate([
    (0, swagger_1.ApiTags)('Venda'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.Controller)('vendas'),
    __metadata("design:paramtypes", [venda_service_1.VendaService,
        auth_service_1.AuthService])
], VendaController);
//# sourceMappingURL=venda.controller.js.map
{"version": 3, "file": "multer.config.js", "sourceRoot": "", "sources": ["../../src/common/multer.config.ts"], "names": [], "mappings": ";;;AAAA,mCAAqC;AACrC,+BAA+B;AAC/B,+BAAoC;AAEvB,QAAA,YAAY,GAAG;IAC1B,OAAO,EAAE,IAAA,oBAAW,EAAC;QACnB,WAAW,EAAE,WAAW;QACxB,QAAQ,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;YAChC,MAAM,UAAU,GAAG,GAAG,IAAA,SAAM,GAAE,GAAG,IAAA,cAAO,EAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;YAC9D,QAAQ,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;QAC7B,CAAC;KACF,CAAC;IACF,UAAU,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;QAClC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,uBAAuB,CAAC,EAAE,CAAC;YACtD,OAAO,QAAQ,CAAC,IAAI,KAAK,CAAC,gCAAgC,CAAC,EAAE,KAAK,CAAC,CAAC;QACtE,CAAC;QACD,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACvB,CAAC;IACD,MAAM,EAAE;QACN,QAAQ,EAAE,CAAC,GAAG,IAAI,GAAG,IAAI;KAC1B;CACF,CAAC"}
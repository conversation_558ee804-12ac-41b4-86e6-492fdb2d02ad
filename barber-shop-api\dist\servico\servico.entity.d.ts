import { Document, Types } from 'mongoose';
export type ServicoDocument = Servico & Document;
export declare class Servico {
    nome: string;
    preco: number;
    duracao: number;
    barbeariaId: Types.ObjectId;
}
export declare const ServicoSchema: import("mongoose").Schema<Servico, import("mongoose").Model<Servico, any, any, any, Document<unknown, any, Servico, any> & Servico & {
    _id: Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Servico, Document<unknown, {}, import("mongoose").FlatRecord<Servico>, {}> & import("mongoose").FlatRecord<Servico> & {
    _id: Types.ObjectId;
} & {
    __v: number;
}>;

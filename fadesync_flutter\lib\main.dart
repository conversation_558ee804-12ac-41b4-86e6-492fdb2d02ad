import 'package:fadesync/agenda.dart';
import 'package:fadesync/configuracoes.dart';
import 'package:fadesync/vendas.dart';
import 'package:fadesync/login';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

void main() {
  runApp(
    ProviderScope( 
      child: FadeSyncApp(),
    ),
  );
}

class FadeSyncApp extends StatelessWidget {
  const FadeSyncApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      title: 'FadeSync',
      theme: ThemeData.light(),
      locale: const Locale('pt', 'BR'),
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('pt', 'BR'),
        Locale('en', 'US'),
      ],
      home: const LoginScreen(),
    );
  }
}

class HomeScreen extends StatefulWidget {
  const HomeScreen({Key? key}) : super(key: key);
  

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _selectedIndex = 1;
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  static List<Widget> _pages = <Widget>[
    Center(child: Text('Vendas')),
    AgendaScreen(),
    ConfiguracoesScreen(), // Placeholder
  ];

  void _onItemTapped(int index) {
    if (index == 3) {
      _scaffoldKey.currentState?.openDrawer();
    } else {
      setState(() {
        _selectedIndex = index;
      });
    }
  }

  void _openPage(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    print(_selectedIndex);
    return Scaffold(
      key: _scaffoldKey,
      // drawer: FadeSyncSidebar(onMenuItemSelected: _openPage),
      appBar: AppBar(title: const Text('FadeSync')),
      body: _pages[_selectedIndex],
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _selectedIndex,
        onTap: _onItemTapped,
        selectedItemColor: const Color.fromARGB(255, 145, 79, 184),
        unselectedItemColor: Colors.grey,
        items: <BottomNavigationBarItem>[
          BottomNavigationBarItem(
            icon: Image.asset(
              'assets/icons/shopping-bag-add.png',
              width: 24,
              height: 24,
              color: _selectedIndex == 0 ? const Color.fromARGB(255, 145, 79, 184) : Colors.grey,
            ),
            label: 'Vendas',
          ),
          BottomNavigationBarItem(
            icon: Image.asset(
              'assets/icons/calendar-lines.png',
              width: 24,
              height: 24,
              color: _selectedIndex == 1 ? const Color.fromARGB(255, 145, 79, 184) : Colors.grey,
            ),
            label: 'Agenda',
          ),
          BottomNavigationBarItem(
            icon: Image.asset(
              'assets/icons/settings.png',
              width: 24,
              height: 24,
              color: _selectedIndex == 2 ? const Color.fromARGB(255, 145, 79, 184) : Colors.grey,
            ),
            label: 'Configurações',
          ),
        ],
      ),
    );
  }
}

// class FadeSyncSidebar extends StatelessWidget {
  // final Function(int) onMenuItemSelected;
  // const FadeSyncSidebar({Key? key, required this.onMenuItemSelected}) : super(key: key);

  // @overide
  // Widgt build(BuildContext context) {
  //   return Drawer(
  //     child: Container(
  //       color: const Color(0xFFF5F5F5),
  //       child: ListView(
  //         padding: EdgeInsets.zero,
  //         children: <Widget>[
  //           const DrawerHeader(
  //             decoration: BoxDecoration(
  //               color: Color.fromARGB(255, 243, 243, 243),
  //             ),
  //             child: Text(
  //               'Configurações',
  //               style: TextStyle(
  //                 color: Color.fromARGB(255, 193, 200, 228),
  //                 fontSize: 24,
  //                 fontWeight: FontWeight.bold,
  //               ),
  //             ),
  //           ),
  //           _buildMenuItem(context, 'dashboard-monitor', 'Dashboard', 0),
  //           _buildMenuItem(context, 'file-invoice-dollar', 'Financeiro', 1),
  //           _buildMenuItem(context, 'employee-man', 'Funcionários', 2),
  //           _buildMenuItem(context, 'barber-shop', 'Serviços', 3),
  //         ],
  //       ),
  //     ),
    // );
  // }

  Widget _buildMenuItem(BuildContext context, String path, String title, int pageIndex) {
    return ListTile(
      leading: Image.asset(
        'assets/icons/${path.toLowerCase()}.png',
        width: 24,
        height: 24,
        color: const Color.fromARGB(255, 138, 134, 134),
      ),
      title: Text(
        title,
        style: const TextStyle(
            color: Color.fromARGB(255, 109, 107, 107),
            fontWeight: FontWeight.bold),
      ),
      onTap: () {
        Navigator.of(context).pop();
        // onMenuItemSelected(pageIndex);
        // Aqui você pode adicionar navegação, se quiser.
      },
    );
  }
// }

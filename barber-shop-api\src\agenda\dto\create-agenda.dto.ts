import { IsDateString, IsString, Is<PERSON>ptional, IsArray, IsEnum } from 'class-validator';
import { StatusHorario } from '../entities/agenda.entity';

export class CreateAgendaDto {
  @IsDateString()
  data: string;

  @IsString()
  horario: string;

  @IsEnum(StatusHorario)
  @IsOptional()
  status?: StatusHorario;

  @IsString()
  @IsOptional()
  clienteNome?: string;

  @IsString()
  @IsOptional()
  clienteEmail?: string;

  @IsArray()
  @IsOptional()
  servicosIds?: string[];
}

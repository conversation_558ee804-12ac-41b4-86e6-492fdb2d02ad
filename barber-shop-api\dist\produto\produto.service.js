"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProdutoService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const produto_entity_1 = require("./entities/produto.entity");
let ProdutoService = class ProdutoService {
    produtoModel;
    constructor(produtoModel) {
        this.produtoModel = produtoModel;
    }
    async create(createProdutoDto, barbeariaId) {
        const ultimoProduto = await this.produtoModel
            .findOne({ barbeariaId: new mongoose_2.Types.ObjectId(barbeariaId) })
            .sort({ codigo: -1 })
            .exec();
        let proximoCodigo = 1;
        if (ultimoProduto && ultimoProduto.codigo) {
            const ultimoNumero = parseInt(ultimoProduto.codigo.replace(/\D/g, ''));
            proximoCodigo = ultimoNumero + 1;
        }
        const codigo = proximoCodigo.toString().padStart(6, '0');
        const produto = new this.produtoModel({
            ...createProdutoDto,
            barbeariaId: new mongoose_2.Types.ObjectId(barbeariaId),
            codigo,
        });
        return produto.save();
    }
    async findAllByBarbearia(barbeariaId, filtros) {
        const query = {
            barbeariaId: new mongoose_2.Types.ObjectId(barbeariaId),
            ativo: true,
        };
        if (filtros?.nome) {
            query.nome = { $regex: filtros.nome, $options: 'i' };
        }
        if (filtros?.valorMin !== undefined || filtros?.valorMax !== undefined) {
            query.valor = {};
            if (filtros.valorMin !== undefined) {
                query.valor.$gte = filtros.valorMin;
            }
            if (filtros.valorMax !== undefined) {
                query.valor.$lte = filtros.valorMax;
            }
        }
        if (filtros?.codigo) {
            query.codigo = { $regex: filtros.codigo, $options: 'i' };
        }
        return this.produtoModel.find(query).sort({ criadoEm: -1 }).exec();
    }
    async findOne(id, barbeariaId) {
        const produto = await this.produtoModel
            .findOne({
            _id: new mongoose_2.Types.ObjectId(id),
            barbeariaId: new mongoose_2.Types.ObjectId(barbeariaId),
            ativo: true,
        })
            .exec();
        if (!produto) {
            throw new common_1.BadRequestException('Produto não encontrado');
        }
        return produto;
    }
    async update(id, updateProdutoDto, barbeariaId) {
        const produto = await this.produtoModel
            .findOneAndUpdate({
            _id: new mongoose_2.Types.ObjectId(id),
            barbeariaId: new mongoose_2.Types.ObjectId(barbeariaId),
        }, { ...updateProdutoDto, atualizadoEm: new Date() }, { new: true })
            .exec();
        if (!produto) {
            throw new common_1.BadRequestException('Produto não encontrado');
        }
        return produto;
    }
    async remove(id, barbeariaId) {
        const result = await this.produtoModel
            .findOneAndUpdate({
            _id: new mongoose_2.Types.ObjectId(id),
            barbeariaId: new mongoose_2.Types.ObjectId(barbeariaId),
        }, { ativo: false, atualizadoEm: new Date() }, { new: true })
            .exec();
        if (!result) {
            throw new common_1.BadRequestException('Produto não encontrado');
        }
    }
};
exports.ProdutoService = ProdutoService;
exports.ProdutoService = ProdutoService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(produto_entity_1.Produto.name)),
    __metadata("design:paramtypes", [mongoose_2.Model])
], ProdutoService);
//# sourceMappingURL=produto.service.js.map
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type ProdutoDocument = Produto & Document;

@Schema()
export class Produto {
  @Prop({ type: Types.ObjectId, ref: 'Barbearia', required: true })
  barbeariaId: Types.ObjectId;

  @Prop({ required: true })
  nome: string;

  @Prop({ required: false })
  descricao?: string;

  @Prop({ required: true })
  valor: number;

  @Prop({ required: true })
  codigo: string;

  @Prop({ default: true })
  ativo: boolean;

  @Prop({ default: Date.now })
  criadoEm: Date;

  @Prop({ default: Date.now })
  atualizadoEm: Date;
}

export const ProdutoSchema = SchemaFactory.createForClass(Produto);

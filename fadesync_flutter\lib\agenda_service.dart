import 'dart:convert';
import 'api_service.dart';

class AgendaService {
  final String token;
  late final ApiService _apiService;

  AgendaService(this.token) {
    _apiService = ApiService(token);
  }

  Future<List<Map<String, dynamic>>> buscarAgendaPorData(String data) async {
    try {
      final response = await _apiService.get('agenda', params: {'data': data});
      
      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.cast<Map<String, dynamic>>();
      } else {
        throw Exception('Erro ao buscar agenda: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Erro ao buscar agenda: $e');
    }
  }

  Future<Map<String, dynamic>> adicionarHorario(Map<String, dynamic> horario) async {
    try {
      final response = await _apiService.post('agenda', horario);
      
      if (response.statusCode == 201) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Erro ao adicionar horário: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Erro ao adicionar horário: $e');
    }
  }

  Future<Map<String, dynamic>> atualizarHorario(String id, Map<String, dynamic> horario) async {
    try {
      final response = await _apiService.put('agenda/$id', horario);
      
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Erro ao atualizar horário: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Erro ao atualizar horário: $e');
    }
  }

  Future<void> removerHorario(String id) async {
    try {
      final response = await _apiService.delete('agenda/$id');

      if (response.statusCode != 200) {
        throw Exception('Erro ao remover horário: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Erro ao remover horário: $e');
    }
  }

  Future<Map<String, dynamic>> verificarConflitosGerarAgenda({
    required String dataInicio,
    required String dataFim,
    required int intervaloMinutos,
    required String horarioInicio,
    required String horarioFim,
    required bool incluirFinalSemana,
  }) async {
    try {
      final dados = {
        'dataInicio': dataInicio,
        'dataFim': dataFim,
        'intervaloMinutos': intervaloMinutos,
        'horarioInicio': horarioInicio,
        'horarioFim': horarioFim,
        'incluirFinalSemana': incluirFinalSemana,
      };

      final response = await _apiService.post('agenda/gerar/verificar', dados);

      if (response.statusCode == 201) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Erro ao verificar conflitos: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Erro ao verificar conflitos: $e');
    }
  }

  Future<Map<String, dynamic>> gerarAgenda({
    required String dataInicio,
    required String dataFim,
    required int intervaloMinutos,
    required String horarioInicio,
    required String horarioFim,
    required bool incluirFinalSemana,
    bool forcarSubstituicao = false,
  }) async {
    try {
      final dados = {
        'dataInicio': dataInicio,
        'dataFim': dataFim,
        'intervaloMinutos': intervaloMinutos,
        'horarioInicio': horarioInicio,
        'horarioFim': horarioFim,
        'incluirFinalSemana': incluirFinalSemana,
        'forcarSubstituicao': forcarSubstituicao,
      };

      final response = await _apiService.post('agenda/gerar', dados);
      
      if (response.statusCode == 201) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Erro ao gerar agenda: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Erro ao gerar agenda: $e');
    }
  }
}

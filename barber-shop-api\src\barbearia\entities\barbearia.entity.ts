import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Document } from "mongoose";

export type BarbeariaDocument = Barbearia & Document;

@Schema()
export class Barbearia {
    @Prop()
    id: String;
    @Prop()
    nome: String;
    @Prop()
    endereco: String;
    @Prop()
    cidade: String;
    @Prop()
    estado: String;
    @Prop()
    horarioAbertura: String;
    @Prop()
    horarioFechamento: String;
    @Prop()
    diasFuncionamento: String;
    @Prop()
    email: String;

}

export const BarbeariaSchema = SchemaFactory.createForClass(Barbearia);

import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type ServicoDocument = Servico & Document;

@Schema()
export class Servico {
  @Prop({ required: true })
  nome: string;

  @Prop({ required: true })
  preco: number;

  @Prop({ required: false })
  duracao: number;

  @Prop({ type: Types.ObjectId, ref: 'Barbearia', required: true })
  barbeariaId: Types.ObjectId;
}

export const ServicoSchema = SchemaFactory.createForClass(Servico);

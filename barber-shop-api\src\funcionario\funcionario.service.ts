import { Injectable, BadRequestException, ConflictException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Funcionario, FuncionarioDocument } from './entities/funcionario.entity';
import { Usuario, UsuarioDocument } from '../usuario/entities/usuario.entity';
import { CreateFuncionarioDto } from './dto/create-funcionario.dto';
import { UpdateFuncionarioDto } from './dto/update-funcionario.dto';
import * as bcrypt from 'bcrypt';

@Injectable()
export class FuncionarioService {
  constructor(
    @InjectModel(Funcionario.name) private funcionarioModel: Model<FuncionarioDocument>,
    @InjectModel(Usuario.name) private usuarioModel: Model<UsuarioDocument>,
  ) {}

  async create(createFuncionarioDto: CreateFuncionarioDto, barbeariaId: string): Promise<Funcionario> {
    // Verificar se CPF já existe
    const cpfExistente = await this.funcionarioModel
      .findOne({ cpf: createFuncionarioDto.cpf })
      .exec();

    if (cpfExistente) {
      throw new ConflictException('CPF já cadastrado');
    }

    // Verificar se email já existe na tabela de usuários
    const emailExistente = await this.usuarioModel
      .findOne({ email: createFuncionarioDto.email })
      .exec();

    if (emailExistente) {
      throw new ConflictException('Email já cadastrado');
    }

    // Criar usuário primeiro
    const senhaTemporaria = this.gerarSenhaTemporaria();
    const senhaHash = await bcrypt.hash(senhaTemporaria, 10);

    const novoUsuario = new this.usuarioModel({
      nome: createFuncionarioDto.nome,
      email: createFuncionarioDto.email,
      senha: senhaHash,
      barbeariaId: new Types.ObjectId(barbeariaId),
    });

    const usuarioSalvo = await novoUsuario.save();

    // Criar funcionário
    const funcionario = new this.funcionarioModel({
      ...createFuncionarioDto,
      barbeariaId: new Types.ObjectId(barbeariaId),
      usuarioId: usuarioSalvo._id,
    });

    const funcionarioSalvo = await funcionario.save();

    // Retornar funcionário com informação da senha temporária
    return {
      ...funcionarioSalvo.toObject(),
      senhaTemporaria,
    } as any;
  }

  async findAllByBarbearia(barbeariaId: string): Promise<Funcionario[]> {
    return this.funcionarioModel
      .find({
        barbeariaId: new Types.ObjectId(barbeariaId),
        ativo: true,
      })
      .populate('usuarioId', 'nome email')
      .sort({ criadoEm: -1 })
      .exec();
  }

  async findOne(id: string, barbeariaId: string): Promise<Funcionario> {
    const funcionario = await this.funcionarioModel
      .findOne({
        _id: new Types.ObjectId(id),
        barbeariaId: new Types.ObjectId(barbeariaId),
        ativo: true,
      })
      .populate('usuarioId', 'nome email')
      .exec();

    if (!funcionario) {
      throw new BadRequestException('Funcionário não encontrado');
    }

    return funcionario;
  }

  async update(id: string, updateFuncionarioDto: UpdateFuncionarioDto, barbeariaId: string): Promise<Funcionario> {
    const funcionario = await this.funcionarioModel
      .findOne({
        _id: new Types.ObjectId(id),
        barbeariaId: new Types.ObjectId(barbeariaId),
      })
      .exec();

    if (!funcionario) {
      throw new BadRequestException('Funcionário não encontrado');
    }

    // Se está atualizando CPF, verificar se não existe outro com o mesmo CPF
    if (updateFuncionarioDto.cpf && updateFuncionarioDto.cpf !== funcionario.cpf) {
      const cpfExistente = await this.funcionarioModel
        .findOne({
          cpf: updateFuncionarioDto.cpf,
          _id: { $ne: new Types.ObjectId(id) },
        })
        .exec();

      if (cpfExistente) {
        throw new ConflictException('CPF já cadastrado');
      }
    }

    // Atualizar funcionário
    const funcionarioAtualizado = await this.funcionarioModel
      .findOneAndUpdate(
        { _id: new Types.ObjectId(id) },
        { ...updateFuncionarioDto, atualizadoEm: new Date() },
        { new: true }
      )
      .populate('usuarioId', 'nome email')
      .exec();

    // Atualizar usuário se necessário
    if (updateFuncionarioDto.nome || updateFuncionarioDto.email) {
      await this.usuarioModel
        .findOneAndUpdate(
          { _id: funcionario.usuarioId },
          {
            ...(updateFuncionarioDto.nome && { nome: updateFuncionarioDto.nome }),
            ...(updateFuncionarioDto.email && { email: updateFuncionarioDto.email }),
          }
        )
        .exec();
    }

    return funcionarioAtualizado!;
  }

  async remove(id: string, barbeariaId: string): Promise<void> {
    const funcionario = await this.funcionarioModel
      .findOneAndUpdate(
        {
          _id: new Types.ObjectId(id),
          barbeariaId: new Types.ObjectId(barbeariaId),
        },
        { ativo: false, atualizadoEm: new Date() }
      )
      .exec();

    if (!funcionario) {
      throw new BadRequestException('Funcionário não encontrado');
    }

    // Não desativar o usuário, apenas o funcionário
    // O usuário pode continuar existindo para histórico
  }

  private gerarSenhaTemporaria(): string {
    return Math.random().toString(36).slice(-8);
  }
}

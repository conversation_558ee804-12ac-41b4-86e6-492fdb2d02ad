"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const mongoose_1 = require("mongoose");
const usuario_entity_1 = require("../usuario/entities/usuario.entity");
const bcrypt = require("bcrypt");
const mongoose_2 = require("@nestjs/mongoose");
let AuthService = class AuthService {
    jwtService;
    userModel;
    constructor(jwtService, userModel) {
        this.jwtService = jwtService;
        this.userModel = userModel;
    }
    async validateUser(username, pass) {
        const user = await this.userModel.findOne({ email: username }).exec();
        if (user && (await bcrypt.compare(pass, user.senha))) {
            const { senha, ...result } = user.toObject();
            return result;
        }
        return null;
    }
    async login(user) {
        const payload = { username: user.email, sub: user._id.toString() };
        console.log(payload);
        return {
            token: this.jwtService.sign(payload),
            usuario: {
                id: user._id.toString(),
                nome: user.nome,
                email: user.email,
                barbeariaId: user.barbeariaId,
            },
            permissoes: [],
        };
    }
    async getUserFromToken(token) {
        try {
            if (token.startsWith('Bearer ')) {
                token = token.slice(7);
            }
            const payload = this.jwtService.verify(token);
            if (!payload.sub) {
                throw new common_1.UnauthorizedException('Token inválido: sub ausente');
            }
            const user = await this.userModel.findOne({ email: payload.username }).exec();
            if (!user) {
                throw new common_1.UnauthorizedException('Usuário não encontrado');
            }
            const { senha, ...userData } = user.toObject();
            return {
                usuario: userData,
                permissoes: [],
            };
        }
        catch (err) {
            throw new common_1.UnauthorizedException('Token inválido ou expirado');
        }
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, mongoose_2.InjectModel)(usuario_entity_1.Usuario.name)),
    __metadata("design:paramtypes", [jwt_1.JwtService,
        mongoose_1.Model])
], AuthService);
//# sourceMappingURL=auth-service.js.map
import {
  Controller,
  Get,
  Post,
  Body,
  Headers,
  Query,
} from '@nestjs/common';
import { Api<PERSON><PERSON><PERSON>, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { VendaService } from './venda.service';
import { CreateVendaDto } from './dto/create-venda.dto';
import { AuthService } from '../auth/auth-service';

@ApiTags('Venda')
@ApiBearerAuth()
@Controller('vendas')
export class VendaController {
  constructor(
    private readonly vendaService: VendaService,
    private readonly authService: AuthService,
  ) {}

  @Post()
  async create(
    @Body() createVendaDto: CreateVendaDto,
    @Headers('authorization') authHeader: string,
  ) {
    const { usuario } = await this.authService.getUserFromToken(authHeader);
    return this.vendaService.create(
      createVendaDto,
      (usuario._id as any).toString(),
      (usuario.barbeariaId as any).toString()
    );
  }

  @Get()
  @ApiQuery({ name: 'dataInicio', required: false })
  @ApiQuery({ name: 'dataFim', required: false })
  async findAll(
    @Headers('authorization') authHeader: string,
    @Query('dataInicio') dataInicio?: string,
    @Query('dataFim') dataFim?: string,
  ) {
    const { usuario } = await this.authService.getUserFromToken(authHeader);
    const barbeariaId = (usuario.barbeariaId as any).toString();

    if (dataInicio && dataFim) {
      return this.vendaService.findByDateRange(
        barbeariaId,
        new Date(dataInicio),
        new Date(dataFim)
      );
    }

    return this.vendaService.findAllByBarbearia(barbeariaId);
  }

  @Get('resumo')
  async getResumo(@Headers('authorization') authHeader: string) {
    const { usuario } = await this.authService.getUserFromToken(authHeader);
    return this.vendaService.getResumoVendas((usuario.barbeariaId as any).toString());
  }
}

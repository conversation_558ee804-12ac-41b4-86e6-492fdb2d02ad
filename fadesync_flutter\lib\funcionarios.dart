import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';
import 'funcionario_service.dart';
import 'auth_provider.dart';

class FuncionariosScreen extends ConsumerStatefulWidget {
  const FuncionariosScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<FuncionariosScreen> createState() => _FuncionariosScreenState();
}

class _FuncionariosScreenState extends ConsumerState<FuncionariosScreen> {
  List<Map<String, dynamic>> funcionarios = [];
  bool isLoading = false;
  String? errorMessage;

  @override
  void initState() {
    super.initState();
    _carregarFuncionarios();
  }

  Future<void> _carregarFuncionarios() async {
    setState(() {
      isLoading = true;
      errorMessage = null;
    });

    try {
      final authState = ref.read(authProvider);
      if (authState.token == null) {
        throw Exception('Token não encontrado');
      }

      final funcionarioService = FuncionarioService(authState.token!);
      final funcionariosData = await funcionarioService.buscarFuncionarios();

      setState(() {
        funcionarios = funcionariosData;
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        errorMessage = e.toString();
        isLoading = false;
      });
    }
  }

  void _mostrarModalFuncionario([Map<String, dynamic>? funcionario]) {
    showDialog(
      context: context,
      builder: (context) => FuncionarioModal(
        funcionario: funcionario,
        onSalvar: (funcionarioData) async {
          try {
            final authState = ref.read(authProvider);
            final funcionarioService = FuncionarioService(authState.token!);

            Map<String, dynamic> resultado;
            if (funcionario == null) {
              resultado = await funcionarioService.criarFuncionario(funcionarioData);
            } else {
              resultado = await funcionarioService.atualizarFuncionario(funcionario['_id'], funcionarioData);
            }

            _carregarFuncionarios();

            // Mostrar senha temporária se for criação
            if (funcionario == null && resultado['senhaTemporaria'] != null) {
              _mostrarSenhaTemporaria(funcionarioData['nome'], resultado['senhaTemporaria']);
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(funcionario == null ? 'Funcionário criado com sucesso!' : 'Funcionário atualizado com sucesso!'),
                  backgroundColor: Colors.green,
                ),
              );
            }
          } catch (e) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Erro: $e'),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
      ),
    );
  }

  void _mostrarSenhaTemporaria(String nome, String senha) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.key, color: Colors.green),
            SizedBox(width: 8),
            Text('Funcionário Criado'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Funcionário $nome criado com sucesso!'),
            const SizedBox(height: 16),
            const Text('Senha temporária gerada:', style: TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      senha,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'monospace',
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () {
                      // Copiar para clipboard
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('Senha copiada!')),
                      );
                    },
                    icon: const Icon(Icons.copy),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'IMPORTANTE: Anote esta senha, ela não será exibida novamente!',
              style: TextStyle(color: Colors.red, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Entendi'),
          ),
        ],
      ),
    );
  }

  void _confirmarInativacao(Map<String, dynamic> funcionario) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.warning, color: Colors.red),
            SizedBox(width: 8),
            Text('Confirmar Inativação'),
          ],
        ),
        content: Text('Deseja realmente inativar o funcionário "${funcionario['nome']}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancelar'),
          ),
          ElevatedButton(
            onPressed: () async {
              try {
                final authState = ref.read(authProvider);
                final funcionarioService = FuncionarioService(authState.token!);
                await funcionarioService.inativarFuncionario(funcionario['_id']);
                Navigator.pop(context);
                _carregarFuncionarios();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Funcionário inativado com sucesso!'),
                    backgroundColor: Colors.green,
                  ),
                );
              } catch (e) {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Erro: $e'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Inativar'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Funcionários'),
        actions: [
          IconButton(
            onPressed: () => _mostrarModalFuncionario(),
            icon: const Icon(Icons.add),
            tooltip: 'Adicionar Funcionário',
          ),
        ],
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : errorMessage != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text('Erro: $errorMessage'),
                      ElevatedButton(
                        onPressed: _carregarFuncionarios,
                        child: const Text('Tentar novamente'),
                      ),
                    ],
                  ),
                )
              : funcionarios.isEmpty
                  ? const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.people, size: 64, color: Colors.grey),
                          SizedBox(height: 16),
                          Text('Nenhum funcionário cadastrado', style: TextStyle(fontSize: 18, color: Colors.grey)),
                        ],
                      ),
                    )
                  : Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: ListView.builder(
                        itemCount: funcionarios.length,
                        itemBuilder: (context, index) {
                          final funcionario = funcionarios[index];
                          final usuario = funcionario['usuarioId'] as Map<String, dynamic>?;

                          return Card(
                            elevation: 2,
                            margin: const EdgeInsets.symmetric(vertical: 4),
                            child: ListTile(
                              contentPadding: const EdgeInsets.all(16),
                              leading: CircleAvatar(
                                backgroundColor: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                                child: Icon(Icons.person, color: Theme.of(context).primaryColor),
                              ),
                              title: Text(funcionario['nome'], style: const TextStyle(fontWeight: FontWeight.bold)),
                              subtitle: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('CPF: ${funcionario['cpf']}'),
                                  Text('Email: ${funcionario['email']}'),
                                ],
                              ),
                              trailing: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  IconButton(
                                    icon: const Icon(Icons.edit, color: Colors.blue),
                                    onPressed: () => _mostrarModalFuncionario(funcionario),
                                  ),
                                  IconButton(
                                    icon: const Icon(Icons.delete, color: Colors.red),
                                    onPressed: () => _confirmarInativacao(funcionario),
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
                    ),
    );
  }
}

class FuncionarioModal extends StatefulWidget {
  final Map<String, dynamic>? funcionario;
  final Function(Map<String, dynamic>) onSalvar;

  const FuncionarioModal({Key? key, this.funcionario, required this.onSalvar}) : super(key: key);

  @override
  State<FuncionarioModal> createState() => _FuncionarioModalState();
}

class _FuncionarioModalState extends State<FuncionarioModal> {
  final _formKey = GlobalKey<FormState>();
  final _nomeController = TextEditingController();
  final _emailController = TextEditingController();
  final _cpfController = TextEditingController();

  final _cpfMask = MaskTextInputFormatter(
    mask: '###.###.###-##',
    filter: {"#": RegExp(r'[0-9]')},
  );

  @override
  void initState() {
    super.initState();
    if (widget.funcionario != null) {
      _nomeController.text = widget.funcionario!['nome'] ?? '';
      _emailController.text = widget.funcionario!['email'] ?? '';
      _cpfController.text = widget.funcionario!['cpf'] ?? '';
    }
  }

  @override
  Widget build(BuildContext context) {
    final isEdit = widget.funcionario != null;

    return AlertDialog(
      title: Row(
        children: [
          Icon(isEdit ? Icons.edit : Icons.person_add, color: Theme.of(context).primaryColor),
          const SizedBox(width: 8),
          Text(isEdit ? 'Editar Funcionário' : 'Novo Funcionário'),
        ],
      ),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: _nomeController,
                decoration: const InputDecoration(
                  labelText: 'Nome Completo',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.person),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Nome é obrigatório';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _emailController,
                decoration: const InputDecoration(
                  labelText: 'Email',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.email),
                ),
                keyboardType: TextInputType.emailAddress,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Email é obrigatório';
                  }
                  if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                    return 'Email inválido';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _cpfController,
                inputFormatters: [_cpfMask],
                decoration: const InputDecoration(
                  labelText: 'CPF',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.badge),
                  hintText: '000.000.000-00',
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'CPF é obrigatório';
                  }
                  if (value.length != 14) {
                    return 'CPF deve ter 11 dígitos';
                  }
                  return null;
                },
              ),
              if (!isEdit) ...[
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue[200]!),
                  ),
                  child: const Row(
                    children: [
                      Icon(Icons.info, color: Colors.blue),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Uma senha temporária será gerada automaticamente para o funcionário.',
                          style: TextStyle(fontSize: 12),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancelar'),
        ),
        ElevatedButton(
          onPressed: _salvar,
          child: Text(isEdit ? 'Salvar' : 'Criar'),
        ),
      ],
    );
  }

  void _salvar() {
    if (!_formKey.currentState!.validate()) return;

    final funcionarioData = {
      'nome': _nomeController.text.trim(),
      'email': _emailController.text.trim(),
      'cpf': _cpfController.text,
    };

    widget.onSalvar(funcionarioData);
    Navigator.pop(context);
  }

  @override
  void dispose() {
    _nomeController.dispose();
    _emailController.dispose();
    _cpfController.dispose();
    super.dispose();
  }
}

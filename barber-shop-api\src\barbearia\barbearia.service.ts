import { Injectable } from '@nestjs/common';
import { CreateBarbeariaDto } from './dto/create-barbearia.dto';
import { UpdateBarbeariaDto } from './dto/update-barbearia.dto';
import { Model, models, model } from 'mongoose';
import { Barbearia, BarbeariaSchema } from './entities/barbearia.entity';
import { AuthService } from 'src/auth/auth-service';
import { UsuarioService } from 'src/usuario/usuario.service';
import { Usuario } from 'src/usuario/entities/usuario.entity';
import { InjectModel } from '@nestjs/mongoose';

@Injectable()
export class BarbeariaService {

  constructor(
    @InjectModel('Barbearia') private readonly barbeariaModel: Model<Barbearia>,
    @InjectModel('Usuario') private readonly userModel: Model<Usuario>,
    private readonly authService: AuthService,
  ) { }

  async findAll(id: string) {
    const user = await this.authService.getUserFromToken(id);
    if (user && user.usuario.barbeariaId) {
      const barbearia = await this.barbeariaModel.findOne({ _id: user.usuario.barbeariaId }).exec();
      if (barbearia) {
        return barbearia;
      }

    } else {

      const barbearia = new this.barbeariaModel({
        nome: '',
        endereco: '',
        cidade: '',
        estado: '',
        horarioAbertura: '',
        horarioFechamento: '',
        diasFuncionamento: '',
        email: '',
      });

      barbearia.save();

      const usuario = await this.userModel.findOne({ email: user.usuario.email });
      if (usuario) {
        usuario.barbeariaId = barbearia._id;
        usuario.save();
      }
      console.log(usuario);

      return barbearia;
    }
  }

  async update(id: string, updateBarbeariaDto: any) {
    return this.barbeariaModel.findByIdAndUpdate(id, updateBarbeariaDto, { new: true }).exec();
  }
}

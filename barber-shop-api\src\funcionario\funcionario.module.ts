import { Mo<PERSON><PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { FuncionarioService } from './funcionario.service';
import { FuncionarioController } from './funcionario.controller';
import { Funcionario, FuncionarioSchema } from './entities/funcionario.entity';
import { Usuario, UsuarioSchema } from '../usuario/entities/usuario.entity';
import { AuthModule } from '../auth/auth.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Funcionario.name, schema: FuncionarioSchema },
      { name: Usuario.name, schema: UsuarioSchema },
    ]),
    AuthModule,
  ],
  controllers: [FuncionarioController],
  providers: [FuncionarioService],
  exports: [FuncionarioService],
})
export class FuncionarioModule {}

"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BarbeariaSchema = exports.Barbearia = void 0;
const mongoose_1 = require("@nestjs/mongoose");
let Barbearia = class Barbearia {
    id;
    nome;
    endereco;
    cidade;
    estado;
    horarioAbertura;
    horarioFechamento;
    diasFuncionamento;
    email;
};
exports.Barbearia = Barbearia;
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], Barbearia.prototype, "id", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], Barbearia.prototype, "nome", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], Barbearia.prototype, "endereco", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], Barbearia.prototype, "cidade", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], Barbearia.prototype, "estado", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], Barbearia.prototype, "horarioAbertura", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], Barbearia.prototype, "horarioFechamento", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], Barbearia.prototype, "diasFuncionamento", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], Barbearia.prototype, "email", void 0);
exports.Barbearia = Barbearia = __decorate([
    (0, mongoose_1.Schema)()
], Barbearia);
exports.BarbeariaSchema = mongoose_1.SchemaFactory.createForClass(Barbearia);
//# sourceMappingURL=barbearia.entity.js.map
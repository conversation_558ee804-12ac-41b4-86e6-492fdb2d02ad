"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FuncionarioController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const funcionario_service_1 = require("./funcionario.service");
const create_funcionario_dto_1 = require("./dto/create-funcionario.dto");
const update_funcionario_dto_1 = require("./dto/update-funcionario.dto");
const auth_service_1 = require("../auth/auth-service");
let FuncionarioController = class FuncionarioController {
    funcionarioService;
    authService;
    constructor(funcionarioService, authService) {
        this.funcionarioService = funcionarioService;
        this.authService = authService;
    }
    async create(createFuncionarioDto, authHeader) {
        const { usuario } = await this.authService.getUserFromToken(authHeader);
        return this.funcionarioService.create(createFuncionarioDto, usuario.barbeariaId.toString());
    }
    async findAll(authHeader) {
        const { usuario } = await this.authService.getUserFromToken(authHeader);
        return this.funcionarioService.findAllByBarbearia(usuario.barbeariaId.toString());
    }
    async findOne(id, authHeader) {
        const { usuario } = await this.authService.getUserFromToken(authHeader);
        return this.funcionarioService.findOne(id, usuario.barbeariaId.toString());
    }
    async update(id, updateFuncionarioDto, authHeader) {
        const { usuario } = await this.authService.getUserFromToken(authHeader);
        return this.funcionarioService.update(id, updateFuncionarioDto, usuario.barbeariaId.toString());
    }
    async remove(id, authHeader) {
        const { usuario } = await this.authService.getUserFromToken(authHeader);
        await this.funcionarioService.remove(id, usuario.barbeariaId.toString());
        return { message: 'Funcionário inativado com sucesso' };
    }
};
exports.FuncionarioController = FuncionarioController;
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Headers)('authorization')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_funcionario_dto_1.CreateFuncionarioDto, String]),
    __metadata("design:returntype", Promise)
], FuncionarioController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Headers)('authorization')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], FuncionarioController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Headers)('authorization')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], FuncionarioController.prototype, "findOne", null);
__decorate([
    (0, common_1.Put)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Headers)('authorization')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_funcionario_dto_1.UpdateFuncionarioDto, String]),
    __metadata("design:returntype", Promise)
], FuncionarioController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Headers)('authorization')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], FuncionarioController.prototype, "remove", null);
exports.FuncionarioController = FuncionarioController = __decorate([
    (0, swagger_1.ApiTags)('Funcionario'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.Controller)('funcionarios'),
    __metadata("design:paramtypes", [funcionario_service_1.FuncionarioService,
        auth_service_1.AuthService])
], FuncionarioController);
//# sourceMappingURL=funcionario.controller.js.map
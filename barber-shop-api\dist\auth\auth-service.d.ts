import { JwtService } from '@nestjs/jwt';
import { Model } from 'mongoose';
import { UsuarioDocument } from 'src/usuario/entities/usuario.entity';
export declare class AuthService {
    private jwtService;
    private userModel;
    constructor(jwtService: JwtService, userModel: Model<UsuarioDocument>);
    validateUser(username: string, pass: string): Promise<any>;
    login(user: any): Promise<{
        token: string;
        usuario: {
            id: any;
            nome: any;
            email: any;
            barbeariaId: any;
        };
        permissoes: never[];
    }>;
    getUserFromToken(token: string): Promise<{
        usuario: {
            id: any;
            nome: String;
            email: String;
            barbeariaId: import("mongoose").Types.ObjectId;
            _id: unknown;
            $locals: Record<string, unknown>;
            $op: "save" | "validate" | "remove" | null;
            $where: Record<string, unknown>;
            baseModelName?: string;
            collection: import("mongoose").Collection;
            db: import("mongoose").Connection;
            errors?: import("mongoose").Error.ValidationError;
            isNew: boolean;
            schema: import("mongoose").Schema;
            __v: number;
        };
        permissoes: never[];
    }>;
}

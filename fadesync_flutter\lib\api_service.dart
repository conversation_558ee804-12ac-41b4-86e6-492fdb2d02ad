import 'dart:convert';
import 'package:http/http.dart' as http;
import 'config.dart';

class ApiService {
  final String token;

  ApiService(this.token);

  Future<http.Response> get(String endpoint, {Map<String, String>? params}) async {
    final uri = Uri.parse('${Config.apiUrl}/$endpoint')
        .replace(queryParameters: params);
    return await http.get(uri, headers: _buildHeaders());
  }

  Future<http.Response> put(String endpoint, Map<String, dynamic> data, {Map<String, String>? params}) async {
    final uri = Uri.parse('${Config.apiUrl}/$endpoint')
        .replace(queryParameters: params);
    return await http.put(uri, headers: _buildHeaders(), body: jsonEncode(data));
  }

    Future<http.Response> post(String endpoint, Map<String, dynamic> data, {Map<String, String>? params}) async {
    final uri = Uri.parse('${Config.apiUrl}/$endpoint')
        .replace(queryParameters: params);
    return await http.post(uri, headers: _buildHeaders(), body: jsonEncode(data));
  }

  Future<http.Response> delete(String endpoint, {Map<String, String>? params}) async {
    final uri = Uri.parse('${Config.apiUrl}/$endpoint')
        .replace(queryParameters: params);
    return await http.delete(uri, headers: _buildHeaders());
  }

  Map<String, String> _buildHeaders() => {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer $token',
  };
}

import { AgendaService } from './agenda.service';
import { CreateAgendaDto } from './dto/create-agenda.dto';
import { UpdateAgendaDto } from './dto/update-agenda.dto';
import { GerarAgendaDto } from './dto/gerar-agenda.dto';
import { AuthService } from '../auth/auth-service';
export declare class AgendaController {
    private readonly agendaService;
    private readonly authService;
    constructor(agendaService: AgendaService, authService: AuthService);
    create(createAgendaDto: CreateAgendaDto, authHeader: string): Promise<import("./entities/agenda.entity").Agenda>;
    findByDate(data: string, authHeader: string): Promise<import("./entities/agenda.entity").Agenda[]>;
    update(id: string, updateAgendaDto: UpdateAgendaDto, authHeader: string): Promise<import("./entities/agenda.entity").Agenda>;
    remove(id: string, authHeader: string): Promise<{
        message: string;
    }>;
    verificarConflitosGerarAgenda(gerarAgendaDto: GerarAgendaDto, authHeader: string): Promise<{
        conflitos: any[];
        totalHorarios: number;
    }>;
    gerarAgenda(body: GerarAgendaDto & {
        forcarSubstituicao?: boolean;
    }, authHeader: string): Promise<{
        criados: number;
        message: string;
    }>;
}

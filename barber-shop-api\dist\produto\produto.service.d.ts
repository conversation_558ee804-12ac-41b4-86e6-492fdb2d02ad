import { Model } from 'mongoose';
import { Produto, ProdutoDocument } from './entities/produto.entity';
import { CreateProdutoDto } from './dto/create-produto.dto';
import { UpdateProdutoDto } from './dto/update-produto.dto';
export declare class ProdutoService {
    private produtoModel;
    constructor(produtoModel: Model<ProdutoDocument>);
    create(createProdutoDto: CreateProdutoDto, barbeariaId: string): Promise<Produto>;
    findAllByBarbearia(barbeariaId: string, filtros?: {
        nome?: string;
        valorMin?: number;
        valorMax?: number;
        codigo?: string;
    }): Promise<Produto[]>;
    findOne(id: string, barbeariaId: string): Promise<Produto>;
    update(id: string, updateProdutoDto: UpdateProdutoDto, barbeariaId: string): Promise<Produto>;
    remove(id: string, barbeariaId: string): Promise<void>;
}

import { Model } from 'mongoose';
import { Venda, VendaDocument } from './entities/venda.entity';
import { ProdutoDocument } from '../produto/entities/produto.entity';
import { CreateVendaDto } from './dto/create-venda.dto';
export declare class VendaService {
    private vendaModel;
    private produtoModel;
    constructor(vendaModel: Model<VendaDocument>, produtoModel: Model<ProdutoDocument>);
    create(createVendaDto: CreateVendaDto, usuarioId: string, barbeariaId: string): Promise<Venda>;
    findAllByBarbearia(barbeariaId: string): Promise<Venda[]>;
    findByDateRange(barbeariaId: string, dataInicio: Date, dataFim: Date): Promise<Venda[]>;
    getResumoVendas(barbeariaId: string): Promise<any>;
}

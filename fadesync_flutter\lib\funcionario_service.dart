import 'dart:convert';
import 'api_service.dart';

class FuncionarioService {
  final String token;
  late final ApiService _apiService;

  FuncionarioService(this.token) {
    _apiService = ApiService(token);
  }

  Future<List<Map<String, dynamic>>> buscarFuncionarios() async {
    try {
      final response = await _apiService.get('funcionarios');
      
      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.cast<Map<String, dynamic>>();
      } else {
        throw Exception('Erro ao buscar funcionários: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Erro ao buscar funcionários: $e');
    }
  }

  Future<Map<String, dynamic>> buscarFuncionario(String id) async {
    try {
      final response = await _apiService.get('funcionarios/$id');
      
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Erro ao buscar funcionário: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Erro ao buscar funcionário: $e');
    }
  }

  Future<Map<String, dynamic>> criarFuncionario(Map<String, dynamic> funcionario) async {
    try {
      final response = await _apiService.post('funcionarios', funcionario);
      
      if (response.statusCode == 201) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Erro ao criar funcionário: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Erro ao criar funcionário: $e');
    }
  }

  Future<Map<String, dynamic>> atualizarFuncionario(String id, Map<String, dynamic> funcionario) async {
    try {
      final response = await _apiService.put('funcionarios/$id', funcionario);
      
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Erro ao atualizar funcionário: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Erro ao atualizar funcionário: $e');
    }
  }

  Future<void> inativarFuncionario(String id) async {
    try {
      final response = await _apiService.delete('funcionarios/$id');
      
      if (response.statusCode != 200) {
        throw Exception('Erro ao inativar funcionário: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Erro ao inativar funcionário: $e');
    }
  }
}

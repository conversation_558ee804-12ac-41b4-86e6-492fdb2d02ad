import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigModule } from '@nestjs/config';
import { BarbeariaModule } from './barbearia/barbearia.module';
import { UsuarioModule } from './usuario/usuario.module';
import { AuthModule } from './auth/auth.module';
import { ServicosModule } from './servico/servico.module';
import { AgendaModule } from './agenda/agenda.module';
import { ProdutoModule } from './produto/produto.module';
import { VendaModule } from './venda/venda.module';
import { FuncionarioModule } from './funcionario/funcionario.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    MongooseModule.forRoot(process.env.MONGODB_URI || ''),
    BarbeariaModule,
    UsuarioModule,
    AuthModule,
    ServicosModule,
    AgendaModule,
    ProdutoModule,
    VendaModule,
    FuncionarioModule,
    // Outros módulos serão adicionados aqui
  ],
})
export class AppModule {}

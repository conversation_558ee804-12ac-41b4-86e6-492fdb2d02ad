import {
  Controller,
  Get,
  Post,
  Body,
  Put,
  Param,
  Delete,
  Headers,
  Query,
} from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { ProdutoService } from './produto.service';
import { CreateProdutoDto } from './dto/create-produto.dto';
import { UpdateProdutoDto } from './dto/update-produto.dto';
import { AuthService } from '../auth/auth-service';

@ApiTags('Produto')
@ApiBearerAuth()
@Controller('produtos')
export class ProdutoController {
  constructor(
    private readonly produtoService: ProdutoService,
    private readonly authService: AuthService,
  ) {}

  @Post()
  async create(
    @Body() createProdutoDto: CreateProdutoDto,
    @Headers('authorization') authHeader: string,
  ) {
    const { usuario } = await this.authService.getUserFromToken(authHeader);
    return this.produtoService.create(createProdutoDto, (usuario.barbeariaId as any).toString());
  }

  @Get()
  @ApiQuery({ name: 'nome', required: false })
  @ApiQuery({ name: 'valorMin', required: false, type: Number })
  @ApiQuery({ name: 'valorMax', required: false, type: Number })
  @ApiQuery({ name: 'codigo', required: false })
  async findAll(
    @Headers('authorization') authHeader: string,
    @Query('nome') nome?: string,
    @Query('valorMin') valorMin?: number,
    @Query('valorMax') valorMax?: number,
    @Query('codigo') codigo?: string,
  ) {
    const { usuario } = await this.authService.getUserFromToken(authHeader);
    const filtros = {
      ...(nome && { nome }),
      ...(valorMin !== undefined && { valorMin: Number(valorMin) }),
      ...(valorMax !== undefined && { valorMax: Number(valorMax) }),
      ...(codigo && { codigo }),
    };
    
    return this.produtoService.findAllByBarbearia(
      (usuario.barbeariaId as any).toString(),
      Object.keys(filtros).length > 0 ? filtros : undefined
    );
  }

  @Get(':id')
  async findOne(
    @Param('id') id: string,
    @Headers('authorization') authHeader: string,
  ) {
    const { usuario } = await this.authService.getUserFromToken(authHeader);
    return this.produtoService.findOne(id, (usuario.barbeariaId as any).toString());
  }

  @Put(':id')
  async update(
    @Param('id') id: string,
    @Body() updateProdutoDto: UpdateProdutoDto,
    @Headers('authorization') authHeader: string,
  ) {
    const { usuario } = await this.authService.getUserFromToken(authHeader);
    return this.produtoService.update(id, updateProdutoDto, (usuario.barbeariaId as any).toString());
  }

  @Delete(':id')
  async remove(
    @Param('id') id: string,
    @Headers('authorization') authHeader: string,
  ) {
    const { usuario } = await this.authService.getUserFromToken(authHeader);
    await this.produtoService.remove(id, (usuario.barbeariaId as any).toString());
    return { message: 'Produto inativado com sucesso' };
  }
}

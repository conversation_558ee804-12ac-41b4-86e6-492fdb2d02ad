import 'dart:convert';
import 'api_service.dart';

class BarbeariaService {
  final ApiService api;

  BarbeariaService(this.api);

  Future<Map<String, dynamic>> fetchConfiguracoes({String? id}) async {
    final response =
        await api.get('barbearias', params: id != null ? {'id': id} : null);
    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Erro ao buscar informações da barbearia');
    }
  }

  Future<void> salvarConfiguracoes(String id, Map<String, dynamic> data) async {
    final response = await api.put('barbearias/${id}', data);
    if (response.statusCode != 200) {
      throw Exception('Erro ao salvar informações');
    }
  }

  Future<List<dynamic>> fetchServicos({String? barbeariaId}) async {
    final response = await api.get('servicos', params: barbeariaId != null ? {'barbeariaId': barbeariaId} : null);
    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Erro ao buscar informações da barbearia');
    }
  }

  Future<void> atualizarServico(String id, Map<String, dynamic> data) async {
    await api.put('servicos/$id', data);
  }

  Future<void> criarServico(Map<String, dynamic> data) async {
    await api.post('servicos', data);
  }

  Future<void> excluirServico(String id) async {
    final response = await api.delete('servicos/$id');
    if (response.statusCode != 200) {
      throw Exception('Erro ao excluir serviço');
    }
  }
}

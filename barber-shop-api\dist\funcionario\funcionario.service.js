"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FuncionarioService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const funcionario_entity_1 = require("./entities/funcionario.entity");
const usuario_entity_1 = require("../usuario/entities/usuario.entity");
const bcrypt = require("bcrypt");
let FuncionarioService = class FuncionarioService {
    funcionarioModel;
    usuarioModel;
    constructor(funcionarioModel, usuarioModel) {
        this.funcionarioModel = funcionarioModel;
        this.usuarioModel = usuarioModel;
    }
    async create(createFuncionarioDto, barbeariaId) {
        const cpfExistente = await this.funcionarioModel
            .findOne({ cpf: createFuncionarioDto.cpf })
            .exec();
        if (cpfExistente) {
            throw new common_1.ConflictException('CPF já cadastrado');
        }
        const emailExistente = await this.usuarioModel
            .findOne({ email: createFuncionarioDto.email })
            .exec();
        if (emailExistente) {
            throw new common_1.ConflictException('Email já cadastrado');
        }
        const senhaTemporaria = this.gerarSenhaTemporaria();
        const senhaHash = await bcrypt.hash(senhaTemporaria, 10);
        const novoUsuario = new this.usuarioModel({
            nome: createFuncionarioDto.nome,
            email: createFuncionarioDto.email,
            senha: senhaHash,
            barbeariaId: new mongoose_2.Types.ObjectId(barbeariaId),
        });
        const usuarioSalvo = await novoUsuario.save();
        const funcionario = new this.funcionarioModel({
            ...createFuncionarioDto,
            barbeariaId: new mongoose_2.Types.ObjectId(barbeariaId),
            usuarioId: usuarioSalvo._id,
        });
        const funcionarioSalvo = await funcionario.save();
        return {
            ...funcionarioSalvo.toObject(),
            senhaTemporaria,
        };
    }
    async findAllByBarbearia(barbeariaId) {
        return this.funcionarioModel
            .find({
            barbeariaId: new mongoose_2.Types.ObjectId(barbeariaId),
            ativo: true,
        })
            .populate('usuarioId', 'nome email')
            .sort({ criadoEm: -1 })
            .exec();
    }
    async findOne(id, barbeariaId) {
        const funcionario = await this.funcionarioModel
            .findOne({
            _id: new mongoose_2.Types.ObjectId(id),
            barbeariaId: new mongoose_2.Types.ObjectId(barbeariaId),
            ativo: true,
        })
            .populate('usuarioId', 'nome email')
            .exec();
        if (!funcionario) {
            throw new common_1.BadRequestException('Funcionário não encontrado');
        }
        return funcionario;
    }
    async update(id, updateFuncionarioDto, barbeariaId) {
        const funcionario = await this.funcionarioModel
            .findOne({
            _id: new mongoose_2.Types.ObjectId(id),
            barbeariaId: new mongoose_2.Types.ObjectId(barbeariaId),
        })
            .exec();
        if (!funcionario) {
            throw new common_1.BadRequestException('Funcionário não encontrado');
        }
        if (updateFuncionarioDto.cpf && updateFuncionarioDto.cpf !== funcionario.cpf) {
            const cpfExistente = await this.funcionarioModel
                .findOne({
                cpf: updateFuncionarioDto.cpf,
                _id: { $ne: new mongoose_2.Types.ObjectId(id) },
            })
                .exec();
            if (cpfExistente) {
                throw new common_1.ConflictException('CPF já cadastrado');
            }
        }
        const funcionarioAtualizado = await this.funcionarioModel
            .findOneAndUpdate({ _id: new mongoose_2.Types.ObjectId(id) }, { ...updateFuncionarioDto, atualizadoEm: new Date() }, { new: true })
            .populate('usuarioId', 'nome email')
            .exec();
        if (updateFuncionarioDto.nome || updateFuncionarioDto.email) {
            await this.usuarioModel
                .findOneAndUpdate({ _id: funcionario.usuarioId }, {
                ...(updateFuncionarioDto.nome && { nome: updateFuncionarioDto.nome }),
                ...(updateFuncionarioDto.email && { email: updateFuncionarioDto.email }),
            })
                .exec();
        }
        return funcionarioAtualizado;
    }
    async remove(id, barbeariaId) {
        const funcionario = await this.funcionarioModel
            .findOneAndUpdate({
            _id: new mongoose_2.Types.ObjectId(id),
            barbeariaId: new mongoose_2.Types.ObjectId(barbeariaId),
        }, { ativo: false, atualizadoEm: new Date() })
            .exec();
        if (!funcionario) {
            throw new common_1.BadRequestException('Funcionário não encontrado');
        }
    }
    gerarSenhaTemporaria() {
        return Math.random().toString(36).slice(-8);
    }
};
exports.FuncionarioService = FuncionarioService;
exports.FuncionarioService = FuncionarioService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(funcionario_entity_1.Funcionario.name)),
    __param(1, (0, mongoose_1.InjectModel)(usuario_entity_1.Usuario.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model])
], FuncionarioService);
//# sourceMappingURL=funcionario.service.js.map
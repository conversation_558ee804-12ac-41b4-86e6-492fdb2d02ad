"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BarbeariaController = void 0;
const common_1 = require("@nestjs/common");
const barbearia_service_1 = require("./barbearia.service");
const update_barbearia_dto_1 = require("./dto/update-barbearia.dto");
const swagger_1 = require("@nestjs/swagger");
let BarbeariaController = class BarbeariaController {
    barbeariaService;
    constructor(barbeariaService) {
        this.barbeariaService = barbeariaService;
    }
    async findAll(authHeader) {
        return this.barbeariaService.findAll(authHeader);
    }
    async update(id, updateBarbeariaDto) {
        return this.barbeariaService.update(id, updateBarbeariaDto);
    }
};
exports.BarbeariaController = BarbeariaController;
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Headers)('authorization')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], BarbeariaController.prototype, "findAll", null);
__decorate([
    (0, common_1.Put)('/:id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_barbearia_dto_1.UpdateBarbeariaDto]),
    __metadata("design:returntype", Promise)
], BarbeariaController.prototype, "update", null);
exports.BarbeariaController = BarbeariaController = __decorate([
    (0, swagger_1.ApiTags)('Barbearia'),
    (0, common_1.Controller)('barbearias'),
    __metadata("design:paramtypes", [barbearia_service_1.BarbeariaService])
], BarbeariaController);
//# sourceMappingURL=barbearia.controller.js.map
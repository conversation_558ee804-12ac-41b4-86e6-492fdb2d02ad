import { Document } from "mongoose";
export type BarbeariaDocument = Barbearia & Document;
export declare class Barbearia {
    id: String;
    nome: String;
    endereco: String;
    cidade: String;
    estado: String;
    horarioAbertura: String;
    horarioFechamento: String;
    diasFuncionamento: String;
    email: String;
}
export declare const BarbeariaSchema: import("mongoose").Schema<Barbearia, import("mongoose").Model<Barbearia, any, any, any, Document<unknown, any, Barbearia, any> & Barbearia & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Barbearia, Document<unknown, {}, import("mongoose").FlatRecord<Barbearia>, {}> & import("mongoose").FlatRecord<Barbearia> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;

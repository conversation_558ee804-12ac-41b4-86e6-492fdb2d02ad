import { Model } from 'mongoose';
import { Servico, ServicoDocument } from './servico.entity';
import { CreateServicoDto } from './dto/create-servico.dto';
export declare class ServicosService {
    private servicoModel;
    constructor(servicoModel: Model<ServicoDocument>);
    create(createServicoDto: CreateServicoDto): Promise<Servico>;
    findAllByBarbearia(barbeariaId: string): Promise<Servico[]>;
    update(id: string, updateData: Partial<CreateServicoDto>): Promise<Servico | null>;
    remove(id: string): Promise<void>;
}

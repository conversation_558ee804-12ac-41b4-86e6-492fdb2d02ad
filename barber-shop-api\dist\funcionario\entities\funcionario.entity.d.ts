import { Document, Types } from 'mongoose';
export type FuncionarioDocument = Funcionario & Document;
export declare class Funcionario {
    barbeariaId: Types.ObjectId;
    usuarioId: Types.ObjectId;
    nome: string;
    cpf: string;
    email: string;
    ativo: boolean;
    criadoEm: Date;
    atualizadoEm: Date;
}
export declare const FuncionarioSchema: import("mongoose").Schema<Funcionario, import("mongoose").Model<Funcionario, any, any, any, Document<unknown, any, Funcionario, any> & Funcionario & {
    _id: Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Funcionario, Document<unknown, {}, import("mongoose").FlatRecord<Funcionario>, {}> & import("mongoose").FlatRecord<Funcionario> & {
    _id: Types.ObjectId;
} & {
    __v: number;
}>;

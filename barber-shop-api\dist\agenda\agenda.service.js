"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgendaService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const agenda_entity_1 = require("./entities/agenda.entity");
let AgendaService = class AgendaService {
    agendaModel;
    constructor(agendaModel) {
        this.agendaModel = agendaModel;
    }
    async create(createAgendaDto, usuarioId) {
        const agenda = new this.agendaModel({
            ...createAgendaDto,
            usuarioId: new mongoose_2.Types.ObjectId(usuarioId),
            servicosIds: createAgendaDto.servicosIds?.map(id => new mongoose_2.Types.ObjectId(id)) || [],
        });
        return agenda.save();
    }
    async findByDateAndUser(data, usuarioId) {
        const startDate = new Date(data);
        const endDate = new Date(data);
        endDate.setDate(endDate.getDate() + 1);
        return this.agendaModel
            .find({
            usuarioId: new mongoose_2.Types.ObjectId(usuarioId),
            data: {
                $gte: startDate,
                $lt: endDate,
            },
        })
            .populate('servicosIds')
            .sort({ horario: 1 })
            .exec();
    }
    async update(id, updateAgendaDto, usuarioId) {
        const updateData = { ...updateAgendaDto };
        if (updateAgendaDto.servicosIds) {
            updateData.servicosIds = updateAgendaDto.servicosIds.map(id => new mongoose_2.Types.ObjectId(id));
        }
        updateData.atualizadoEm = new Date();
        const agenda = await this.agendaModel
            .findOneAndUpdate({ _id: new mongoose_2.Types.ObjectId(id), usuarioId: new mongoose_2.Types.ObjectId(usuarioId) }, updateData, { new: true })
            .populate('servicosIds')
            .exec();
        if (!agenda) {
            throw new common_1.BadRequestException('Horário não encontrado');
        }
        return agenda;
    }
    async remove(id, usuarioId) {
        const result = await this.agendaModel
            .deleteOne({ _id: new mongoose_2.Types.ObjectId(id), usuarioId: new mongoose_2.Types.ObjectId(usuarioId) })
            .exec();
        if (result.deletedCount === 0) {
            throw new common_1.BadRequestException('Horário não encontrado');
        }
    }
    async gerarAgenda(gerarAgendaDto, usuarioId) {
        const { dataInicio, dataFim, intervaloMinutos, horarioInicio, horarioFim, incluirFinalSemana } = gerarAgendaDto;
        const inicio = new Date(dataInicio);
        const fim = new Date(dataFim);
        const diffTime = Math.abs(fim.getTime() - inicio.getTime());
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        if (diffDays > 60) {
            throw new common_1.BadRequestException('O período máximo para geração é de 2 meses (60 dias)');
        }
        const horariosParaCriar = [];
        const currentDate = new Date(inicio);
        while (currentDate <= fim) {
            const dayOfWeek = currentDate.getDay();
            if (!incluirFinalSemana && (dayOfWeek === 0 || dayOfWeek === 6)) {
                currentDate.setDate(currentDate.getDate() + 1);
                continue;
            }
            const horarios = this.gerarHorariosDoDia(horarioInicio, horarioFim, intervaloMinutos);
            for (const horario of horarios) {
                horariosParaCriar.push({
                    usuarioId: new mongoose_2.Types.ObjectId(usuarioId),
                    data: new Date(currentDate),
                    horario,
                    status: agenda_entity_1.StatusHorario.LIVRE,
                    servicosIds: [],
                    criadoEm: new Date(),
                    atualizadoEm: new Date(),
                });
            }
            currentDate.setDate(currentDate.getDate() + 1);
        }
        const horariosExistentes = await this.agendaModel.find({
            usuarioId: new mongoose_2.Types.ObjectId(usuarioId),
            data: { $gte: inicio, $lte: fim },
        }).exec();
        const horariosExistentesSet = new Set(horariosExistentes.map(h => `${h.data.toISOString().split('T')[0]}_${h.horario}`));
        const horariosNovos = horariosParaCriar.filter(h => {
            const key = `${h.data.toISOString().split('T')[0]}_${h.horario}`;
            return !horariosExistentesSet.has(key);
        });
        if (horariosNovos.length > 0) {
            await this.agendaModel.insertMany(horariosNovos);
        }
        return {
            criados: horariosNovos.length,
            message: `${horariosNovos.length} horários criados com sucesso. ${horariosParaCriar.length - horariosNovos.length} horários já existiam.`
        };
    }
    gerarHorariosDoDia(horarioInicio, horarioFim, intervaloMinutos) {
        const horarios = [];
        const [horaInicio, minutoInicio] = horarioInicio.split(':').map(Number);
        const [horaFim, minutoFim] = horarioFim.split(':').map(Number);
        const inicioEmMinutos = horaInicio * 60 + minutoInicio;
        const fimEmMinutos = horaFim * 60 + minutoFim;
        for (let minutos = inicioEmMinutos; minutos < fimEmMinutos; minutos += intervaloMinutos) {
            const horas = Math.floor(minutos / 60);
            const mins = minutos % 60;
            horarios.push(`${horas.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`);
        }
        return horarios;
    }
};
exports.AgendaService = AgendaService;
exports.AgendaService = AgendaService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(agenda_entity_1.Agenda.name)),
    __metadata("design:paramtypes", [mongoose_2.Model])
], AgendaService);
//# sourceMappingURL=agenda.service.js.map
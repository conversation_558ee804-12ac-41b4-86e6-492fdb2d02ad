import { BarbeariaService } from './barbearia.service';
import { UpdateBarbeariaDto } from './dto/update-barbearia.dto';
export declare class BarbeariaController {
    private readonly barbeariaService;
    constructor(barbeariaService: BarbeariaService);
    findAll(authHeader: string): Promise<(import("mongoose").Document<unknown, {}, import("./entities/barbearia.entity").Barbearia, {}> & import("./entities/barbearia.entity").Barbearia & {
        _id: import("mongoose").Types.ObjectId;
    } & {
        __v: number;
    }) | undefined>;
    update(id: string, updateBarbeariaDto: UpdateBarbeariaDto): Promise<(import("mongoose").Document<unknown, {}, import("./entities/barbearia.entity").Barbearia, {}> & import("./entities/barbearia.entity").Barbearia & {
        _id: import("mongoose").Types.ObjectId;
    } & {
        __v: number;
    }) | null>;
}

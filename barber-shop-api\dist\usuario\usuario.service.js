"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsuarioService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const bcrypt = require("bcrypt");
const usuario_entity_1 = require("./entities/usuario.entity");
let UsuarioService = class UsuarioService {
    userModel;
    constructor(userModel) {
        this.userModel = userModel;
    }
    async create(createUsuarioDto) {
        const existsUser = await this.userModel.findOne({ email: createUsuarioDto.email }).exec();
        if (existsUser) {
            throw new common_1.ConflictException('Já existe um usuário com esse login');
        }
        const hashedPassword = await bcrypt.hash(createUsuarioDto.senha, 10);
        const newUser = new this.userModel({
            ...createUsuarioDto,
            senha: hashedPassword,
        });
        return newUser.save();
    }
};
exports.UsuarioService = UsuarioService;
exports.UsuarioService = UsuarioService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(usuario_entity_1.Usuario.name)),
    __metadata("design:paramtypes", [mongoose_2.Model])
], UsuarioService);
//# sourceMappingURL=usuario.service.js.map
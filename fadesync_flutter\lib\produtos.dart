import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';
import 'produto_service.dart';
import 'auth_provider.dart';

class ProdutosScreen extends ConsumerStatefulWidget {
  const ProdutosScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<ProdutosScreen> createState() => _ProdutosScreenState();
}

class _ProdutosScreenState extends ConsumerState<ProdutosScreen> {
  List<Map<String, dynamic>> produtos = [];
  bool isLoading = false;
  String? errorMessage;

  // Filtros
  final _nomeController = TextEditingController();
  final _codigoController = TextEditingController();
  final _valorMinController = TextEditingController();
  final _valorMaxController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _carregarProdutos();
  }

  Future<void> _carregarProdutos() async {
    setState(() {
      isLoading = true;
      errorMessage = null;
    });

    try {
      final authState = ref.read(authProvider);
      if (authState.token == null) {
        throw Exception('Token não encontrado');
      }

      final produtoService = ProdutoService(authState.token!);
      
      // Aplicar filtros se houver
      final nome = _nomeController.text.isNotEmpty ? _nomeController.text : null;
      final codigo = _codigoController.text.isNotEmpty ? _codigoController.text : null;
      final valorMin = _valorMinController.text.isNotEmpty 
          ? double.tryParse(_valorMinController.text.replaceAll(RegExp(r'[R\$\s\.]'), '').replaceAll(',', '.'))
          : null;
      final valorMax = _valorMaxController.text.isNotEmpty 
          ? double.tryParse(_valorMaxController.text.replaceAll(RegExp(r'[R\$\s\.]'), '').replaceAll(',', '.'))
          : null;

      final produtosData = await produtoService.buscarProdutos(
        nome: nome,
        codigo: codigo,
        valorMin: valorMin,
        valorMax: valorMax,
      );

      setState(() {
        produtos = produtosData;
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        errorMessage = e.toString();
        isLoading = false;
      });
    }
  }

  void _mostrarModalProduto([Map<String, dynamic>? produto]) {
    showDialog(
      context: context,
      builder: (context) => ProdutoModal(
        produto: produto,
        onSalvar: (produtoData) async {
          try {
            final authState = ref.read(authProvider);
            final produtoService = ProdutoService(authState.token!);

            if (produto == null) {
              await produtoService.criarProduto(produtoData);
            } else {
              await produtoService.atualizarProduto(produto['_id'], produtoData);
            }

            _carregarProdutos();
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(produto == null ? 'Produto criado com sucesso!' : 'Produto atualizado com sucesso!'),
                backgroundColor: Colors.green,
              ),
            );
          } catch (e) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Erro: $e'),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
      ),
    );
  }

  void _confirmarInativacao(Map<String, dynamic> produto) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.warning, color: Colors.red),
            SizedBox(width: 8),
            Text('Confirmar Inativação'),
          ],
        ),
        content: Text('Deseja realmente inativar o produto "${produto['nome']}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancelar'),
          ),
          ElevatedButton(
            onPressed: () async {
              try {
                final authState = ref.read(authProvider);
                final produtoService = ProdutoService(authState.token!);
                await produtoService.inativarProduto(produto['_id']);
                Navigator.pop(context);
                _carregarProdutos();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Produto inativado com sucesso!'),
                    backgroundColor: Colors.green,
                  ),
                );
              } catch (e) {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Erro: $e'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Inativar'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Produtos'),
        actions: [
          IconButton(
            onPressed: () => _mostrarModalProduto(),
            icon: const Icon(Icons.add),
            tooltip: 'Adicionar Produto',
          ),
        ],
      ),
      body: Column(
        children: [
          // Filtros
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.grey[100],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: _nomeController,
                        decoration: const InputDecoration(
                          labelText: 'Nome',
                          border: OutlineInputBorder(),
                          isDense: true,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: TextField(
                        controller: _codigoController,
                        decoration: const InputDecoration(
                          labelText: 'Código',
                          border: OutlineInputBorder(),
                          isDense: true,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: _valorMinController,
                        decoration: const InputDecoration(
                          labelText: 'Valor Mín.',
                          border: OutlineInputBorder(),
                          isDense: true,
                        ),
                        keyboardType: TextInputType.number,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: TextField(
                        controller: _valorMaxController,
                        decoration: const InputDecoration(
                          labelText: 'Valor Máx.',
                          border: OutlineInputBorder(),
                          isDense: true,
                        ),
                        keyboardType: TextInputType.number,
                      ),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton(
                      onPressed: _carregarProdutos,
                      child: const Text('Filtrar'),
                    ),
                  ],
                ),
              ],
            ),
          ),
          // Lista
          Expanded(
            child: isLoading
                ? const Center(child: CircularProgressIndicator())
                : errorMessage != null
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text('Erro: $errorMessage'),
                            ElevatedButton(
                              onPressed: _carregarProdutos,
                              child: const Text('Tentar novamente'),
                            ),
                          ],
                        ),
                      )
                    : produtos.isEmpty
                        ? const Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(Icons.inventory, size: 64, color: Colors.grey),
                                SizedBox(height: 16),
                                Text('Nenhum produto encontrado', style: TextStyle(fontSize: 18, color: Colors.grey)),
                              ],
                            ),
                          )
                        : Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: ListView.builder(
                              itemCount: produtos.length,
                              itemBuilder: (context, index) {
                                final produto = produtos[index];
                                return Card(
                                  elevation: 2,
                                  margin: const EdgeInsets.symmetric(vertical: 4),
                                  child: ListTile(
                                    contentPadding: const EdgeInsets.all(16),
                                    leading: CircleAvatar(
                                      backgroundColor: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                                      child: Icon(Icons.inventory, color: Theme.of(context).primaryColor),
                                    ),
                                    title: Text(produto['nome'], style: const TextStyle(fontWeight: FontWeight.bold)),
                                    subtitle: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text('Código: ${produto['codigo']}'),
                                        Text(
                                          'R\$ ${produto['valor'].toStringAsFixed(2).replaceAll('.', ',')}',
                                          style: TextStyle(color: Colors.green[700], fontWeight: FontWeight.w500),
                                        ),
                                        if (produto['descricao'] != null && produto['descricao'].isNotEmpty)
                                          Text(produto['descricao'], style: const TextStyle(fontSize: 12)),
                                      ],
                                    ),
                                    trailing: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        IconButton(
                                          icon: const Icon(Icons.edit, color: Colors.blue),
                                          onPressed: () => _mostrarModalProduto(produto),
                                        ),
                                        IconButton(
                                          icon: const Icon(Icons.delete, color: Colors.red),
                                          onPressed: () => _confirmarInativacao(produto),
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _nomeController.dispose();
    _codigoController.dispose();
    _valorMinController.dispose();
    _valorMaxController.dispose();
    super.dispose();
  }
}

class ProdutoModal extends StatefulWidget {
  final Map<String, dynamic>? produto;
  final Function(Map<String, dynamic>) onSalvar;

  const ProdutoModal({Key? key, this.produto, required this.onSalvar}) : super(key: key);

  @override
  State<ProdutoModal> createState() => _ProdutoModalState();
}

class _ProdutoModalState extends State<ProdutoModal> {
  final _formKey = GlobalKey<FormState>();
  final _nomeController = TextEditingController();
  final _descricaoController = TextEditingController();
  final _valorController = TextEditingController();

  final _valorMask = MaskTextInputFormatter(
    mask: 'R\$ ###.###,##',
    filter: {"#": RegExp(r'[0-9]')},
    type: MaskAutoCompletionType.lazy,
  );

  @override
  void initState() {
    super.initState();
    if (widget.produto != null) {
      _nomeController.text = widget.produto!['nome'] ?? '';
      _descricaoController.text = widget.produto!['descricao'] ?? '';
      _valorController.text = 'R\$ ${widget.produto!['valor'].toStringAsFixed(2).replaceAll('.', ',')}';
    }
  }

  @override
  Widget build(BuildContext context) {
    final isEdit = widget.produto != null;

    return AlertDialog(
      title: Row(
        children: [
          Icon(isEdit ? Icons.edit : Icons.add, color: Theme.of(context).primaryColor),
          const SizedBox(width: 8),
          Text(isEdit ? 'Editar Produto' : 'Novo Produto'),
        ],
      ),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: _nomeController,
                decoration: const InputDecoration(
                  labelText: 'Nome do Produto',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.inventory),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Nome é obrigatório';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _descricaoController,
                decoration: const InputDecoration(
                  labelText: 'Descrição (opcional)',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.description),
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _valorController,
                inputFormatters: [_valorMask],
                decoration: const InputDecoration(
                  labelText: 'Valor',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.attach_money),
                  hintText: 'R\$ 0,00',
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Valor é obrigatório';
                  }
                  final valorString = value.replaceAll(RegExp(r'[R\$\s\.]'), '').replaceAll(',', '.');
                  final valor = double.tryParse(valorString);
                  if (valor == null || valor <= 0) {
                    return 'Valor deve ser maior que zero';
                  }
                  return null;
                },
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancelar'),
        ),
        ElevatedButton(
          onPressed: _salvar,
          child: Text(isEdit ? 'Salvar' : 'Criar'),
        ),
      ],
    );
  }

  void _salvar() {
    if (!_formKey.currentState!.validate()) return;

    final valorString = _valorController.text
        .replaceAll(RegExp(r'[R\$\s\.]'), '')
        .replaceAll(',', '.');
    final valor = double.parse(valorString);

    final produtoData = {
      'nome': _nomeController.text.trim(),
      'descricao': _descricaoController.text.trim().isNotEmpty ? _descricaoController.text.trim() : null,
      'valor': valor,
    };

    widget.onSalvar(produtoData);
    Navigator.pop(context);
  }

  @override
  void dispose() {
    _nomeController.dispose();
    _descricaoController.dispose();
    _valorController.dispose();
    super.dispose();
  }
}

import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type VendaDocument = Venda & Document;

@Schema()
export class Venda {
  @Prop({ type: Types.ObjectId, ref: 'Barbearia', required: true })
  barbeariaId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'Produto', required: true })
  produtoId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'Usuario', required: true })
  usuarioId: Types.ObjectId;

  @Prop({ required: true })
  valorOriginal: number;

  @Prop({ required: true })
  valorVenda: number;

  @Prop({ required: true })
  quantidade: number;

  @Prop({ default: Date.now })
  dataVenda: Date;

  @Prop({ default: Date.now })
  criadoEm: Date;
}

export const VendaSchema = SchemaFactory.createForClass(Venda);

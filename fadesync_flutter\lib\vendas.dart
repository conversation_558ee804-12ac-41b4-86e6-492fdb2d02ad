import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';
import 'venda_service.dart';
import 'produto_service.dart';
import 'auth_provider.dart';

class VendasScreen extends ConsumerStatefulWidget {
  const VendasScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<VendasScreen> createState() => _VendasScreenState();
}

class _VendasScreenState extends ConsumerState<VendasScreen> {
  List<Map<String, dynamic>> vendas = [];
  bool isLoading = false;
  String? errorMessage;

  @override
  void initState() {
    super.initState();
    _carregarVendas();
  }

  Future<void> _carregarVendas() async {
    setState(() {
      isLoading = true;
      errorMessage = null;
    });

    try {
      final authState = ref.read(authProvider);
      if (authState.token == null) {
        throw Exception('Token não encontrado');
      }

      final vendaService = VendaService(authState.token!);
      final vendasData = await vendaService.buscarVendas();

      setState(() {
        vendas = vendasData;
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        errorMessage = e.toString();
        isLoading = false;
      });
    }
  }

  void _mostrarModalVenda() {
    showDialog(
      context: context,
      builder: (context) => VendaModal(
        onSalvar: (vendaData) async {
          try {
            final authState = ref.read(authProvider);
            final vendaService = VendaService(authState.token!);

            await vendaService.criarVenda(vendaData);

            _carregarVendas();
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Venda registrada com sucesso!'),
                backgroundColor: Colors.green,
              ),
            );
          } catch (e) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Erro: $e'),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Vendas'),
        actions: [
          IconButton(
            onPressed: _mostrarModalVenda,
            icon: const Icon(Icons.add),
            tooltip: 'Nova Venda',
          ),
        ],
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : errorMessage != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text('Erro: $errorMessage'),
                      ElevatedButton(
                        onPressed: _carregarVendas,
                        child: const Text('Tentar novamente'),
                      ),
                    ],
                  ),
                )
              : vendas.isEmpty
                  ? const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.shopping_cart, size: 64, color: Colors.grey),
                          SizedBox(height: 16),
                          Text('Nenhuma venda registrada', style: TextStyle(fontSize: 18, color: Colors.grey)),
                        ],
                      ),
                    )
                  : Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: ListView.builder(
                        itemCount: vendas.length,
                        itemBuilder: (context, index) {
                          final venda = vendas[index];
                          final produto = venda['produtoId'] as Map<String, dynamic>?;
                          final dataVenda = DateTime.parse(venda['dataVenda']);
                          final valorTotal = venda['valorVenda'] * venda['quantidade'];

                          return Card(
                            elevation: 2,
                            margin: const EdgeInsets.symmetric(vertical: 4),
                            child: ListTile(
                              contentPadding: const EdgeInsets.all(16),
                              leading: CircleAvatar(
                                backgroundColor: Colors.green.withValues(alpha: 0.1),
                                child: const Icon(Icons.shopping_cart, color: Colors.green),
                              ),
                              title: Text(
                                produto?['nome'] ?? 'Produto não encontrado',
                                style: const TextStyle(fontWeight: FontWeight.bold),
                              ),
                              subtitle: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('Código: ${produto?['codigo'] ?? 'N/A'}'),
                                  Text('Quantidade: ${venda['quantidade']}'),
                                  Text('Valor unitário: R\$ ${venda['valorVenda'].toStringAsFixed(2).replaceAll('.', ',')}'),
                                  if (venda['valorOriginal'] != venda['valorVenda'])
                                    Text(
                                      'Valor original: R\$ ${venda['valorOriginal'].toStringAsFixed(2).replaceAll('.', ',')}',
                                      style: const TextStyle(
                                        decoration: TextDecoration.lineThrough,
                                        color: Colors.grey,
                                      ),
                                    ),
                                  Text(
                                    DateFormat('dd/MM/yyyy HH:mm').format(dataVenda),
                                    style: TextStyle(color: Colors.grey[600]),
                                  ),
                                ],
                              ),
                              trailing: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  Text(
                                    'Total',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                  Text(
                                    'R\$ ${valorTotal.toStringAsFixed(2).replaceAll('.', ',')}',
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.green,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
                    ),
    );
  }
}

class VendaModal extends ConsumerStatefulWidget {
  final Function(Map<String, dynamic>) onSalvar;

  const VendaModal({Key? key, required this.onSalvar}) : super(key: key);

  @override
  ConsumerState<VendaModal> createState() => _VendaModalState();
}

class _VendaModalState extends ConsumerState<VendaModal> {
  final _formKey = GlobalKey<FormState>();
  final _quantidadeController = TextEditingController(text: '1');
  final _valorController = TextEditingController();

  List<Map<String, dynamic>> produtos = [];
  Map<String, dynamic>? produtoSelecionado;
  bool isLoadingProdutos = false;

  final _valorMask = MaskTextInputFormatter(
    mask: 'R\$ ###.###,##',
    filter: {"#": RegExp(r'[0-9]')},
    type: MaskAutoCompletionType.lazy,
  );

  @override
  void initState() {
    super.initState();
    _carregarProdutos();
  }

  Future<void> _carregarProdutos() async {
    setState(() {
      isLoadingProdutos = true;
    });

    try {
      final authState = ref.read(authProvider);
      final produtoService = ProdutoService(authState.token!);
      final produtosData = await produtoService.buscarProdutos();

      setState(() {
        produtos = produtosData;
        isLoadingProdutos = false;
      });
    } catch (e) {
      setState(() {
        isLoadingProdutos = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Erro ao carregar produtos: $e')),
      );
    }
  }

  void _onProdutoSelecionado(Map<String, dynamic>? produto) {
    setState(() {
      produtoSelecionado = produto;
      if (produto != null) {
        _valorController.text = 'R\$ ${produto['valor'].toStringAsFixed(2).replaceAll('.', ',')}';
      } else {
        _valorController.clear();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Row(
        children: [
          Icon(Icons.add_shopping_cart, color: Colors.green),
          SizedBox(width: 8),
          Text('Nova Venda'),
        ],
      ),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Seleção de produto
              isLoadingProdutos
                  ? const CircularProgressIndicator()
                  : DropdownButtonFormField<Map<String, dynamic>>(
                      value: produtoSelecionado,
                      decoration: const InputDecoration(
                        labelText: 'Produto',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.inventory),
                      ),
                      items: produtos.map((produto) {
                        return DropdownMenuItem<Map<String, dynamic>>(
                          value: produto,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(produto['nome']),
                              Text(
                                'Código: ${produto['codigo']} - R\$ ${produto['valor'].toStringAsFixed(2).replaceAll('.', ',')}',
                                style: const TextStyle(fontSize: 12, color: Colors.grey),
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                      onChanged: _onProdutoSelecionado,
                      validator: (value) {
                        if (value == null) {
                          return 'Selecione um produto';
                        }
                        return null;
                      },
                    ),
              const SizedBox(height: 16),
              // Quantidade
              TextFormField(
                controller: _quantidadeController,
                decoration: const InputDecoration(
                  labelText: 'Quantidade',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.numbers),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Quantidade é obrigatória';
                  }
                  final quantidade = int.tryParse(value);
                  if (quantidade == null || quantidade <= 0) {
                    return 'Quantidade deve ser maior que zero';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              // Valor
              TextFormField(
                controller: _valorController,
                inputFormatters: [_valorMask],
                decoration: const InputDecoration(
                  labelText: 'Valor Unitário',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.attach_money),
                  hintText: 'R\$ 0,00',
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Valor é obrigatório';
                  }
                  final valorString = value.replaceAll(RegExp(r'[R\$\s\.]'), '').replaceAll(',', '.');
                  final valor = double.tryParse(valorString);
                  if (valor == null || valor <= 0) {
                    return 'Valor deve ser maior que zero';
                  }
                  return null;
                },
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancelar'),
        ),
        ElevatedButton(
          onPressed: _salvar,
          child: const Text('Registrar Venda'),
        ),
      ],
    );
  }

  void _salvar() {
    if (!_formKey.currentState!.validate()) return;

    final valorString = _valorController.text
        .replaceAll(RegExp(r'[R\$\s\.]'), '')
        .replaceAll(',', '.');
    final valor = double.parse(valorString);
    final quantidade = int.parse(_quantidadeController.text);

    final vendaData = {
      'produtoId': produtoSelecionado!['_id'],
      'valorVenda': valor,
      'quantidade': quantidade,
    };

    widget.onSalvar(vendaData);
    Navigator.pop(context);
  }

  @override
  void dispose() {
    _quantidadeController.dispose();
    _valorController.dispose();
    super.dispose();
  }
}

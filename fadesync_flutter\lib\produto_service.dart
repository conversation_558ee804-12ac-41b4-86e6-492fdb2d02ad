import 'dart:convert';
import 'api_service.dart';

class ProdutoService {
  final String token;
  late final ApiService _apiService;

  ProdutoService(this.token) {
    _apiService = ApiService(token);
  }

  Future<List<Map<String, dynamic>>> buscarProdutos({
    String? nome,
    double? valorMin,
    double? valorMax,
    String? codigo,
  }) async {
    try {
      final params = <String, String>{};
      if (nome != null) params['nome'] = nome;
      if (valorMin != null) params['valorMin'] = valorMin.toString();
      if (valorMax != null) params['valorMax'] = valorMax.toString();
      if (codigo != null) params['codigo'] = codigo;

      final response = await _apiService.get('produtos', params: params);
      
      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.cast<Map<String, dynamic>>();
      } else {
        throw Exception('Erro ao buscar produtos: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Erro ao buscar produtos: $e');
    }
  }

  Future<Map<String, dynamic>> buscarProduto(String id) async {
    try {
      final response = await _apiService.get('produtos/$id');
      
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Erro ao buscar produto: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Erro ao buscar produto: $e');
    }
  }

  Future<Map<String, dynamic>> criarProduto(Map<String, dynamic> produto) async {
    try {
      final response = await _apiService.post('produtos', produto);
      
      if (response.statusCode == 201) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Erro ao criar produto: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Erro ao criar produto: $e');
    }
  }

  Future<Map<String, dynamic>> atualizarProduto(String id, Map<String, dynamic> produto) async {
    try {
      final response = await _apiService.put('produtos/$id', produto);
      
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Erro ao atualizar produto: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Erro ao atualizar produto: $e');
    }
  }

  Future<void> inativarProduto(String id) async {
    try {
      final response = await _apiService.delete('produtos/$id');
      
      if (response.statusCode != 200) {
        throw Exception('Erro ao inativar produto: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Erro ao inativar produto: $e');
    }
  }
}

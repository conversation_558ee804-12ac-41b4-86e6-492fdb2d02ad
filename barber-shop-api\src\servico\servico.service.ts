import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Servico, ServicoDocument } from './servico.entity';
import { CreateServicoDto } from './dto/create-servico.dto';

@Injectable()
export class ServicosService {
  constructor(
    @InjectModel(Servico.name) private servicoModel: Model<ServicoDocument>,
  ) { }

  async create(createServicoDto: CreateServicoDto): Promise<Servico> {
    const createdServico = new this.servicoModel(createServicoDto);
    createdServico.barbeariaId = new Types.ObjectId(createServicoDto.barbeariaId);
    return createdServico.save();
  }

  async findAllByBarbearia(barbeariaId: string): Promise<Servico[]> {
    return await this.servicoModel.find({ barbeariaId: new Types.ObjectId(barbeariaId) }).exec();
  }


  async update(id: string, updateData: Partial<CreateServicoDto>): Promise<Servico | null> {
    const dataToUpdate: any = { ...updateData };

    if (dataToUpdate.barbeariaId) {
      dataToUpdate.barbeariaId = new Types.ObjectId(dataToUpdate.barbeariaId);
    }

    return this.servicoModel.findByIdAndUpdate(id, dataToUpdate, { new: true }).exec();
  }

  async remove(id: string) {
    this.servicoModel.findByIdAndDelete({ _id: id }).exec();
  }
}

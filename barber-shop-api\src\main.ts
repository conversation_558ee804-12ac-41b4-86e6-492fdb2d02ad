import { NestFactory } from '@nestjs/core';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { ValidationPipe } from '@nestjs/common';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  // Configuração CORS para integração com o frontend React
  app.enableCors();
  
  // Validação global de DTOs
  app.useGlobalPipes(new ValidationPipe({ transform: true }));
  
  // Configuração do Swagger
  const config = new DocumentBuilder()
    .setTitle('Barber Shop API')
    .setDescription('API para gerenciamento de barbearia')
    .setVersion('1.0')
    .addBearerAuth()
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api-docs', app, document);
  
  await app.listen(process.env.PORT ?? 3000);
}
bootstrap();

import 'package:fadesync/api_service.dart';
import 'package:fadesync/auth_provider.dart';
import 'package:fadesync/barbearia_service.dart';
import 'package:fadesync/servicos.dart';
import 'package:fadesync/produtos.dart';
import 'package:fadesync/funcionarios.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class ConfiguracoesScreen extends StatefulWidget {
  const ConfiguracoesScreen({Key? key}) : super(key: key);

  @override
  _ConfiguracoesScreenState createState() => _ConfiguracoesScreenState();
}

class _ConfiguracoesScreenState extends State<ConfiguracoesScreen> {
  // Lista de opções do menu
  final List<Map<String, String>> opcoes = [
    {'icone': 'dashboard-monitor', 'titulo': 'Dashboard'},
    {'icone': 'file-invoice-dollar', 'titulo': 'Financeiro'},
    {'icone': 'employee-man', 'titulo': 'Funcion<PERSON>rios'},
    {'icone': 'barber-shop', 'titulo': 'Serviços'},
    {'icone': 'inventory', 'titulo': 'Produtos'},
  ];

  // Controllers dos campos
  final TextEditingController nomeController = TextEditingController(text: '');
  final TextEditingController enderecoController =
      TextEditingController(text: '');
  final TextEditingController cidadeController =
      TextEditingController(text: '');
  final TextEditingController estadoController =
      TextEditingController(text: '');
  final TextEditingController horarioAberturaController =
      TextEditingController(text: '');
  final TextEditingController horarioFechamentoController =
      TextEditingController(text: '');
  final TextEditingController diasFuncionamentoController =
      TextEditingController(text: '');
  final TextEditingController emailController = TextEditingController(text: '');

  final _formKey = GlobalKey<FormState>();

  String? _barbeariaId;
  bool _loading = true;
  late final ApiService _api;
  late final BarbeariaService _service;
  String _token = '';

  @override
  void initState() {
    super.initState();
    _iniciarServicosECarregar();
  }

  Future<void> _iniciarServicosECarregar() async {
    try {
      // Para pegar o token do authProvider sem ConsumerStatefulWidget,
      // usamos o ProviderScope.containerOf(context).
      // Porém, no initState, context pode não estar pronto para isso,
      // então usamos addPostFrameCallback para pegar depois.
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        final container = ProviderScope.containerOf(context, listen: false);
        final authState = container.read(authProvider);
        final token = authState.token!;

        _api = ApiService(token);
        _service = BarbeariaService(_api);
        _token = token;

        await _carregarConfiguracoes();

        setState(() {
          _loading = false;
        });
      });
    } catch (e) {
      setState(() {
        _loading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Erro ao iniciar serviços: $e')),
      );
    }
  }

  Future<void> _carregarConfiguracoes() async {
    try {
      final barbeariaData = await _service.fetchConfiguracoes();

      setState(() {
        _barbeariaId = barbeariaData['_id']?.toString();
        nomeController.text = barbeariaData['nome'] ?? '';
        enderecoController.text = barbeariaData['endereco'] ?? '';
        cidadeController.text = barbeariaData['cidade'] ?? '';
        estadoController.text = barbeariaData['estado'] ?? '';
        horarioAberturaController.text = barbeariaData['horarioAbertura'] ?? '';
        horarioFechamentoController.text =
            barbeariaData['horarioFechamento'] ?? '';
        diasFuncionamentoController.text =
            barbeariaData['diasFuncionamento'] ?? '';
        emailController.text = barbeariaData['email'] ?? '';
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Erro ao carregar configurações: $e')),
      );
    }
  }

  Future<void> _salvarFormulario() async {
    if (!_formKey.currentState!.validate()) return;

    if (_barbeariaId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('ID da barbearia não encontrado.')),
      );
      return;
    }

    final data = {
      'nome': nomeController.text,
      'endereco': enderecoController.text,
      'cidade': cidadeController.text,
      'estado': estadoController.text,
      'horarioAbertura': horarioAberturaController.text,
      'horarioFechamento': horarioFechamentoController.text,
      'diasFuncionamento': diasFuncionamentoController.text,
      'email': emailController.text,
    };

    try {
      await _service.salvarConfiguracoes(_barbeariaId!, data);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Informações salvas com sucesso!')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Erro ao salvar informações: $e')),
      );
    }
  }

  @override
  void dispose() {
    nomeController.dispose();
    enderecoController.dispose();
    cidadeController.dispose();
    estadoController.dispose();
    horarioAberturaController.dispose();
    horarioFechamentoController.dispose();
    diasFuncionamentoController.dispose();
    emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_loading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Configurações'),
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _salvarFormulario,
            tooltip: 'Salvar',
          )
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Menu horizontal
            SizedBox(
              height: 100,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: opcoes.length,
                itemBuilder: (context, index) {
                  final opcao = opcoes[index];
                  return Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 12.0),
                    child: InkWell(
                      onTap: () {
                        switch (opcao['titulo']) {
                          case 'Serviços':
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => ServicosScreen(api: _api),
                              ),
                            );
                            break;
                          case 'Produtos':
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const ProdutosScreen(),
                              ),
                            );
                            break;
                          case 'Funcionários':
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const FuncionariosScreen(),
                              ),
                            );
                            break;
                          default:
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(content: Text('${opcao['titulo']} em desenvolvimento')),
                            );
                        }
                      },
                      child: Column(
                        children: [
                          CircleAvatar(
                            radius: 30,
                            backgroundColor: Colors.blueAccent.withValues(alpha: 0.1),
                            child: opcao['icone'] == 'inventory'
                                ? const Icon(
                                    Icons.inventory,
                                    size: 30,
                                    color: Color.fromARGB(255, 113, 115, 121),
                                  )
                                : Image.asset(
                                    'assets/icons/${opcao['icone']}.png',
                                    width: 30,
                                    height: 30,
                                    color: const Color.fromARGB(255, 113, 115, 121),
                                  ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            opcao['titulo']!,
                            style: const TextStyle(fontSize: 14),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),

            const SizedBox(height: 24),

            const Text(
              'Informações',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),

            const SizedBox(height: 12),

            Form(
              key: _formKey,
              child: Column(
                children: [
                  TextFormField(
                    controller: nomeController,
                    decoration: const InputDecoration(
                      labelText: 'Nome',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) => (value == null || value.isEmpty)
                        ? 'Preencha o nome'
                        : null,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: enderecoController,
                    decoration: const InputDecoration(
                      labelText: 'Endereço',
                      border: OutlineInputBorder(),
                    ),
                    maxLines: 2,
                    validator: (value) => (value == null || value.isEmpty)
                        ? 'Preencha o endereço'
                        : null,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: cidadeController,
                    decoration: const InputDecoration(
                      labelText: 'Cidade',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) => (value == null || value.isEmpty)
                        ? 'Preencha a cidade'
                        : null,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: estadoController,
                    decoration: const InputDecoration(
                      labelText: 'Estado',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) => (value == null || value.isEmpty)
                        ? 'Preencha o estado'
                        : null,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: horarioAberturaController,
                    decoration: const InputDecoration(
                      labelText: 'Horário Abertura',
                      border: OutlineInputBorder(),
                      hintText: 'Ex: 08:00',
                    ),
                    validator: (value) => (value == null || value.isEmpty)
                        ? 'Preencha o horário de abertura'
                        : null,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: horarioFechamentoController,
                    decoration: const InputDecoration(
                      labelText: 'Horário Fechamento',
                      border: OutlineInputBorder(),
                      hintText: 'Ex: 18:00',
                    ),
                    validator: (value) => (value == null || value.isEmpty)
                        ? 'Preencha o horário de fechamento'
                        : null,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: diasFuncionamentoController,
                    decoration: const InputDecoration(
                      labelText: 'Dias de Funcionamento',
                      border: OutlineInputBorder(),
                      hintText: 'Ex: Seg - Sab',
                    ),
                    validator: (value) => (value == null || value.isEmpty)
                        ? 'Preencha os dias de funcionamento'
                        : null,
                  ),
                  const SizedBox(height: 24),
                  TextFormField(
                    controller: emailController,
                    decoration: const InputDecoration(
                      labelText: 'Email',
                      border: OutlineInputBorder(),
                      hintText: 'Ex: <EMAIL>',
                    ),
                    validator: (value) => (value == null || value.isEmpty)
                        ? 'Preencha o email'
                        : null,
                  ),
                  const SizedBox(height: 24),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

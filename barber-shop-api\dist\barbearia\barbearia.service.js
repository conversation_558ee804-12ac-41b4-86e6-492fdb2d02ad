"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BarbeariaService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("mongoose");
const auth_service_1 = require("../auth/auth-service");
const mongoose_2 = require("@nestjs/mongoose");
let BarbeariaService = class BarbeariaService {
    barbeariaModel;
    userModel;
    authService;
    constructor(barbeariaModel, userModel, authService) {
        this.barbeariaModel = barbeariaModel;
        this.userModel = userModel;
        this.authService = authService;
    }
    async findAll(id) {
        const user = await this.authService.getUserFromToken(id);
        if (user && user.usuario.barbeariaId) {
            const barbearia = await this.barbeariaModel.findOne({ _id: user.usuario.barbeariaId }).exec();
            if (barbearia) {
                return barbearia;
            }
        }
        else {
            const barbearia = new this.barbeariaModel({
                nome: '',
                endereco: '',
                cidade: '',
                estado: '',
                horarioAbertura: '',
                horarioFechamento: '',
                diasFuncionamento: '',
                email: '',
            });
            barbearia.save();
            const usuario = await this.userModel.findOne({ email: user.usuario.email });
            if (usuario) {
                usuario.barbeariaId = barbearia._id;
                usuario.save();
            }
            console.log(usuario);
            return barbearia;
        }
    }
    async update(id, updateBarbeariaDto) {
        return this.barbeariaModel.findByIdAndUpdate(id, updateBarbeariaDto, { new: true }).exec();
    }
};
exports.BarbeariaService = BarbeariaService;
exports.BarbeariaService = BarbeariaService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_2.InjectModel)('Barbearia')),
    __param(1, (0, mongoose_2.InjectModel)('Usuario')),
    __metadata("design:paramtypes", [mongoose_1.Model,
        mongoose_1.Model,
        auth_service_1.AuthService])
], BarbeariaService);
//# sourceMappingURL=barbearia.service.js.map
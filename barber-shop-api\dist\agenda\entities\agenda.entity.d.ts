import { Document, Types } from 'mongoose';
export type AgendaDocument = Agenda & Document;
export declare enum StatusHorario {
    LIVRE = "LIVRE",
    AGENDADO = "AGENDADO",
    BLOQUEADO = "BLOQUEADO",
    ATENDIDO = "ATENDIDO"
}
export declare class Agenda {
    usuarioId: Types.ObjectId;
    data: Date;
    horario: string;
    status: StatusHorario;
    clienteNome?: string;
    clienteEmail?: string;
    servicosIds: Types.ObjectId[];
    criadoEm: Date;
    atualizadoEm: Date;
}
export declare const AgendaSchema: import("mongoose").Schema<Agenda, import("mongoose").Model<Agenda, any, any, any, Document<unknown, any, Agenda, any> & Agenda & {
    _id: Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Agenda, Document<unknown, {}, import("mongoose").FlatRecord<Agenda>, {}> & import("mongoose").FlatRecord<Agenda> & {
    _id: Types.ObjectId;
} & {
    __v: number;
}>;

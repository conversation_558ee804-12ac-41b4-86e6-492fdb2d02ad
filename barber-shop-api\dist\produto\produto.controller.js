"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProdutoController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const produto_service_1 = require("./produto.service");
const create_produto_dto_1 = require("./dto/create-produto.dto");
const update_produto_dto_1 = require("./dto/update-produto.dto");
const auth_service_1 = require("../auth/auth-service");
let ProdutoController = class ProdutoController {
    produtoService;
    authService;
    constructor(produtoService, authService) {
        this.produtoService = produtoService;
        this.authService = authService;
    }
    async create(createProdutoDto, authHeader) {
        const { usuario } = await this.authService.getUserFromToken(authHeader);
        return this.produtoService.create(createProdutoDto, usuario.barbeariaId.toString());
    }
    async findAll(authHeader, nome, valorMin, valorMax, codigo) {
        const { usuario } = await this.authService.getUserFromToken(authHeader);
        const filtros = {
            ...(nome && { nome }),
            ...(valorMin !== undefined && { valorMin: Number(valorMin) }),
            ...(valorMax !== undefined && { valorMax: Number(valorMax) }),
            ...(codigo && { codigo }),
        };
        return this.produtoService.findAllByBarbearia(usuario.barbeariaId.toString(), Object.keys(filtros).length > 0 ? filtros : undefined);
    }
    async findOne(id, authHeader) {
        const { usuario } = await this.authService.getUserFromToken(authHeader);
        return this.produtoService.findOne(id, usuario.barbeariaId.toString());
    }
    async update(id, updateProdutoDto, authHeader) {
        const { usuario } = await this.authService.getUserFromToken(authHeader);
        return this.produtoService.update(id, updateProdutoDto, usuario.barbeariaId.toString());
    }
    async remove(id, authHeader) {
        const { usuario } = await this.authService.getUserFromToken(authHeader);
        await this.produtoService.remove(id, usuario.barbeariaId.toString());
        return { message: 'Produto inativado com sucesso' };
    }
};
exports.ProdutoController = ProdutoController;
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Headers)('authorization')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_produto_dto_1.CreateProdutoDto, String]),
    __metadata("design:returntype", Promise)
], ProdutoController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiQuery)({ name: 'nome', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'valorMin', required: false, type: Number }),
    (0, swagger_1.ApiQuery)({ name: 'valorMax', required: false, type: Number }),
    (0, swagger_1.ApiQuery)({ name: 'codigo', required: false }),
    __param(0, (0, common_1.Headers)('authorization')),
    __param(1, (0, common_1.Query)('nome')),
    __param(2, (0, common_1.Query)('valorMin')),
    __param(3, (0, common_1.Query)('valorMax')),
    __param(4, (0, common_1.Query)('codigo')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Number, Number, String]),
    __metadata("design:returntype", Promise)
], ProdutoController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Headers)('authorization')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], ProdutoController.prototype, "findOne", null);
__decorate([
    (0, common_1.Put)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Headers)('authorization')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_produto_dto_1.UpdateProdutoDto, String]),
    __metadata("design:returntype", Promise)
], ProdutoController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Headers)('authorization')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], ProdutoController.prototype, "remove", null);
exports.ProdutoController = ProdutoController = __decorate([
    (0, swagger_1.ApiTags)('Produto'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.Controller)('produtos'),
    __metadata("design:paramtypes", [produto_service_1.ProdutoService,
        auth_service_1.AuthService])
], ProdutoController);
//# sourceMappingURL=produto.controller.js.map
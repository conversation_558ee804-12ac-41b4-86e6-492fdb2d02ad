"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgendaSchema = exports.Agenda = exports.StatusHorario = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
var StatusHorario;
(function (StatusHorario) {
    StatusHorario["LIVRE"] = "LIVRE";
    StatusHorario["AGENDADO"] = "AGENDADO";
    StatusHorario["BLOQUEADO"] = "BLOQUEADO";
    StatusHorario["ATENDIDO"] = "ATENDIDO";
})(StatusHorario || (exports.StatusHorario = StatusHorario = {}));
let Agenda = class Agenda {
    usuarioId;
    data;
    horario;
    status;
    clienteNome;
    clienteEmail;
    servicosIds;
    criadoEm;
    atualizadoEm;
};
exports.Agenda = Agenda;
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose_2.Types.ObjectId, ref: 'Usuario', required: true }),
    __metadata("design:type", mongoose_2.Types.ObjectId)
], Agenda.prototype, "usuarioId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", Date)
], Agenda.prototype, "data", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], Agenda.prototype, "horario", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        type: String,
        enum: StatusHorario,
        default: StatusHorario.LIVRE
    }),
    __metadata("design:type", String)
], Agenda.prototype, "status", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false }),
    __metadata("design:type", String)
], Agenda.prototype, "clienteNome", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false }),
    __metadata("design:type", String)
], Agenda.prototype, "clienteEmail", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: [{ type: mongoose_2.Types.ObjectId, ref: 'Servico' }], default: [] }),
    __metadata("design:type", Array)
], Agenda.prototype, "servicosIds", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: Date.now }),
    __metadata("design:type", Date)
], Agenda.prototype, "criadoEm", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: Date.now }),
    __metadata("design:type", Date)
], Agenda.prototype, "atualizadoEm", void 0);
exports.Agenda = Agenda = __decorate([
    (0, mongoose_1.Schema)()
], Agenda);
exports.AgendaSchema = mongoose_1.SchemaFactory.createForClass(Agenda);
//# sourceMappingURL=agenda.entity.js.map
import 'dart:convert';
import 'api_service.dart';

class VendaService {
  final String token;
  late final ApiService _apiService;

  VendaService(this.token) {
    _apiService = ApiService(token);
  }

  Future<List<Map<String, dynamic>>> buscarVendas({
    String? dataInicio,
    String? dataFim,
  }) async {
    try {
      final params = <String, String>{};
      if (dataInicio != null) params['dataInicio'] = dataInicio;
      if (dataFim != null) params['dataFim'] = dataFim;

      final response = await _apiService.get('vendas', params: params);
      
      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.cast<Map<String, dynamic>>();
      } else {
        throw Exception('Erro ao buscar vendas: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Erro ao buscar vendas: $e');
    }
  }

  Future<Map<String, dynamic>> criarVenda(Map<String, dynamic> venda) async {
    try {
      final response = await _apiService.post('vendas', venda);
      
      if (response.statusCode == 201) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Erro ao criar venda: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Erro ao criar venda: $e');
    }
  }

  Future<Map<String, dynamic>> buscarResumoVendas() async {
    try {
      final response = await _apiService.get('vendas/resumo');
      
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Erro ao buscar resumo: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Erro ao buscar resumo: $e');
    }
  }
}

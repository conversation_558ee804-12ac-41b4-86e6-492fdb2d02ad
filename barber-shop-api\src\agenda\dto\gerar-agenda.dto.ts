import { IsDateString, Is<PERSON><PERSON>ber, IsBoolean, IsString, Min, <PERSON>, <PERSON> } from 'class-validator';

export class G<PERSON>rAgendaDto {
  @IsDateString()
  dataInicio: string;

  @IsDateString()
  dataFim: string;

  @IsNumber()
  @Min(15)
  @Max(120)
  intervaloMinutos: number;

  @IsString()
  @Matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, { message: '<PERSON>r<PERSON><PERSON> de início deve estar no formato HH:MM' })
  horarioInicio: string; // formato "08:00"

  @IsString()
  @Matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, { message: 'Hor<PERSON>rio de fim deve estar no formato HH:MM' })
  horarioFim: string; // formato "18:00"

  @IsBoolean()
  incluirFinalSemana: boolean;
}

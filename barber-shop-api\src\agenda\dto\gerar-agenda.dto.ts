import { IsDateString, IsN<PERSON>ber, IsBoolean, IsString, Min, Max } from 'class-validator';

export class GerarAgendaDto {
  @IsDateString()
  dataInicio: string;

  @IsDateString()
  dataFim: string;

  @IsNumber()
  @Min(15)
  @Max(120)
  intervaloMinutos: number;

  @IsString()
  horarioInicio: string; // formato "08:00"

  @IsString()
  horarioFim: string; // formato "18:00"

  @IsBoolean()
  incluirFinalSemana: boolean;
}

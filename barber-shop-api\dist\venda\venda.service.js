"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VendaService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const venda_entity_1 = require("./entities/venda.entity");
const produto_entity_1 = require("../produto/entities/produto.entity");
let VendaService = class VendaService {
    vendaModel;
    produtoModel;
    constructor(vendaModel, produtoModel) {
        this.vendaModel = vendaModel;
        this.produtoModel = produtoModel;
    }
    async create(createVendaDto, usuarioId, barbeariaId) {
        const produto = await this.produtoModel
            .findOne({
            _id: new mongoose_2.Types.ObjectId(createVendaDto.produtoId),
            barbeariaId: new mongoose_2.Types.ObjectId(barbeariaId),
            ativo: true,
        })
            .exec();
        if (!produto) {
            throw new common_1.BadRequestException('Produto não encontrado');
        }
        const venda = new this.vendaModel({
            ...createVendaDto,
            produtoId: new mongoose_2.Types.ObjectId(createVendaDto.produtoId),
            usuarioId: new mongoose_2.Types.ObjectId(usuarioId),
            barbeariaId: new mongoose_2.Types.ObjectId(barbeariaId),
            valorOriginal: produto.valor,
        });
        return venda.save();
    }
    async findAllByBarbearia(barbeariaId) {
        return this.vendaModel
            .find({ barbeariaId: new mongoose_2.Types.ObjectId(barbeariaId) })
            .populate('produtoId', 'nome codigo')
            .populate('usuarioId', 'nome email')
            .sort({ dataVenda: -1 })
            .exec();
    }
    async findByDateRange(barbeariaId, dataInicio, dataFim) {
        return this.vendaModel
            .find({
            barbeariaId: new mongoose_2.Types.ObjectId(barbeariaId),
            dataVenda: {
                $gte: dataInicio,
                $lte: dataFim,
            },
        })
            .populate('produtoId', 'nome codigo')
            .populate('usuarioId', 'nome email')
            .sort({ dataVenda: -1 })
            .exec();
    }
    async getResumoVendas(barbeariaId) {
        const hoje = new Date();
        hoje.setHours(0, 0, 0, 0);
        const amanha = new Date(hoje);
        amanha.setDate(amanha.getDate() + 1);
        const [vendasHoje, totalVendas, valorTotal] = await Promise.all([
            this.vendaModel.countDocuments({
                barbeariaId: new mongoose_2.Types.ObjectId(barbeariaId),
                dataVenda: { $gte: hoje, $lt: amanha },
            }),
            this.vendaModel.countDocuments({
                barbeariaId: new mongoose_2.Types.ObjectId(barbeariaId),
            }),
            this.vendaModel.aggregate([
                { $match: { barbeariaId: new mongoose_2.Types.ObjectId(barbeariaId) } },
                {
                    $group: {
                        _id: null,
                        total: { $sum: { $multiply: ['$valorVenda', '$quantidade'] } },
                    },
                },
            ]),
        ]);
        return {
            vendasHoje,
            totalVendas,
            valorTotal: valorTotal[0]?.total || 0,
        };
    }
};
exports.VendaService = VendaService;
exports.VendaService = VendaService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(venda_entity_1.Venda.name)),
    __param(1, (0, mongoose_1.InjectModel)(produto_entity_1.Produto.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model])
], VendaService);
//# sourceMappingURL=venda.service.js.map
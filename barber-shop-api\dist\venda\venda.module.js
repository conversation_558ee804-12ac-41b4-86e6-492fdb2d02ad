"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VendaModule = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const venda_service_1 = require("./venda.service");
const venda_controller_1 = require("./venda.controller");
const venda_entity_1 = require("./entities/venda.entity");
const produto_entity_1 = require("../produto/entities/produto.entity");
const auth_module_1 = require("../auth/auth.module");
let VendaModule = class VendaModule {
};
exports.VendaModule = VendaModule;
exports.VendaModule = VendaModule = __decorate([
    (0, common_1.Module)({
        imports: [
            mongoose_1.MongooseModule.forFeature([
                { name: venda_entity_1.Venda.name, schema: venda_entity_1.VendaSchema },
                { name: produto_entity_1.Produto.name, schema: produto_entity_1.ProdutoSchema },
            ]),
            auth_module_1.AuthModule,
        ],
        controllers: [venda_controller_1.VendaController],
        providers: [venda_service_1.VendaService],
        exports: [venda_service_1.VendaService],
    })
], VendaModule);
//# sourceMappingURL=venda.module.js.map
import { Model } from 'mongoose';
import { Agenda, AgendaDocument } from './entities/agenda.entity';
import { CreateAgendaDto } from './dto/create-agenda.dto';
import { UpdateAgendaDto } from './dto/update-agenda.dto';
import { GerarAgendaDto } from './dto/gerar-agenda.dto';
export declare class AgendaService {
    private agendaModel;
    constructor(agendaModel: Model<AgendaDocument>);
    create(createAgendaDto: CreateAgendaDto, usuarioId: string): Promise<Agenda>;
    findByDateAndUser(data: string, usuarioId: string): Promise<Agenda[]>;
    update(id: string, updateAgendaDto: UpdateAgendaDto, usuarioId: string): Promise<Agenda>;
    remove(id: string, usuarioId: string): Promise<void>;
    verificarConflitosGerarAgenda(gerarAgendaDto: GerarAgendaDto, usuarioId: string): Promise<{
        conflitos: any[];
        totalHorarios: number;
    }>;
    gerarAgenda(gerarAgendaDto: GerarAgendaDto, usuarioId: string, forcarSubstituicao?: boolean): Promise<{
        criados: number;
        message: string;
    }>;
    private gerarHorariosDoDia;
}

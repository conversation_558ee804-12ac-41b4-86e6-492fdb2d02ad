import { Injectable, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Venda, VendaDocument } from './entities/venda.entity';
import { Produto, ProdutoDocument } from '../produto/entities/produto.entity';
import { CreateVendaDto } from './dto/create-venda.dto';

@Injectable()
export class VendaService {
  constructor(
    @InjectModel(Venda.name) private vendaModel: Model<VendaDocument>,
    @InjectModel(Produto.name) private produtoModel: Model<ProdutoDocument>,
  ) {}

  async create(createVendaDto: CreateVendaDto, usuarioId: string, barbeariaId: string): Promise<Venda> {
    // Buscar o produto para obter o valor original
    const produto = await this.produtoModel
      .findOne({
        _id: new Types.ObjectId(createVendaDto.produtoId),
        barbeariaId: new Types.ObjectId(barbeariaId),
        ativo: true,
      })
      .exec();

    if (!produto) {
      throw new BadRequestException('Produto não encontrado');
    }

    const venda = new this.vendaModel({
      ...createVendaDto,
      produtoId: new Types.ObjectId(createVendaDto.produtoId),
      usuarioId: new Types.ObjectId(usuarioId),
      barbeariaId: new Types.ObjectId(barbeariaId),
      valorOriginal: produto.valor,
    });

    return venda.save();
  }

  async findAllByBarbearia(barbeariaId: string): Promise<Venda[]> {
    return this.vendaModel
      .find({ barbeariaId: new Types.ObjectId(barbeariaId) })
      .populate('produtoId', 'nome codigo')
      .populate('usuarioId', 'nome email')
      .sort({ dataVenda: -1 })
      .exec();
  }

  async findByDateRange(
    barbeariaId: string,
    dataInicio: Date,
    dataFim: Date
  ): Promise<Venda[]> {
    return this.vendaModel
      .find({
        barbeariaId: new Types.ObjectId(barbeariaId),
        dataVenda: {
          $gte: dataInicio,
          $lte: dataFim,
        },
      })
      .populate('produtoId', 'nome codigo')
      .populate('usuarioId', 'nome email')
      .sort({ dataVenda: -1 })
      .exec();
  }

  async getResumoVendas(barbeariaId: string): Promise<any> {
    const hoje = new Date();
    hoje.setHours(0, 0, 0, 0);
    const amanha = new Date(hoje);
    amanha.setDate(amanha.getDate() + 1);

    const [vendasHoje, totalVendas, valorTotal] = await Promise.all([
      this.vendaModel.countDocuments({
        barbeariaId: new Types.ObjectId(barbeariaId),
        dataVenda: { $gte: hoje, $lt: amanha },
      }),
      this.vendaModel.countDocuments({
        barbeariaId: new Types.ObjectId(barbeariaId),
      }),
      this.vendaModel.aggregate([
        { $match: { barbeariaId: new Types.ObjectId(barbeariaId) } },
        {
          $group: {
            _id: null,
            total: { $sum: { $multiply: ['$valorVenda', '$quantidade'] } },
          },
        },
      ]),
    ]);

    return {
      vendasHoje,
      totalVendas,
      valorTotal: valorTotal[0]?.total || 0,
    };
  }
}

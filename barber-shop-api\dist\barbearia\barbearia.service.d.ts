import { Model } from 'mongoose';
import { Barbearia } from './entities/barbearia.entity';
import { AuthService } from 'src/auth/auth-service';
import { Usuario } from 'src/usuario/entities/usuario.entity';
export declare class BarbeariaService {
    private readonly barbeariaModel;
    private readonly userModel;
    private readonly authService;
    constructor(barbeariaModel: Model<Barbearia>, userModel: Model<Usuario>, authService: AuthService);
    findAll(id: string): Promise<(import("mongoose").Document<unknown, {}, Barbearia, {}> & Barbearia & {
        _id: import("mongoose").Types.ObjectId;
    } & {
        __v: number;
    }) | undefined>;
    update(id: string, updateBarbeariaDto: any): Promise<(import("mongoose").Document<unknown, {}, Barbearia, {}> & Barbearia & {
        _id: import("mongoose").Types.ObjectId;
    } & {
        __v: number;
    }) | null>;
}
